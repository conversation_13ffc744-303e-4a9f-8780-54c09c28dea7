# Stripe Webhook Setup

This document explains how to set up the Stripe webhook to ensure subscription updates are properly processed.

## Prerequisites

- Node.js installed
- Stripe account with API keys
- Supabase project with functions enabled
- Stripe CLI installed (for testing)

## Setup Steps

1. **Install dependencies**

```bash
npm install
```

2. **Configure environment variables**

Make sure your `.env` file contains the following variables:

```
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_vZGKmr0OFVV2ZIv4eFAFO3uI6Pkx4pWm
VITE_SUPABASE_URL=https://pajqstbgncpbpcaffbpm.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

3. **Deploy the Stripe function and update the webhook (all-in-one)**

```bash
npm run deploy-stripe
```

This script will:
- Deploy the Stripe webhook function to Supabase
- Check for existing webhooks in your Stripe account
- Update an existing webhook or create a new one
- Configure it to listen for the necessary events

## Testing the Webhook

1. **Start the webhook listener**

```bash
npm run test-webhook
```

This will start the Stripe CLI in listen mode and forward events to your webhook endpoint.

2. **Trigger a test event**

In a separate terminal, run:

```bash
npm run trigger-webhook [event]
```

Where `[event]` is one of:
- `1` or `checkout` for `checkout.session.completed`
- `2` or `created` for `customer.subscription.created`
- `3` or `updated` for `customer.subscription.updated`
- `4` or `deleted` for `customer.subscription.deleted`

Example:
```bash
npm run trigger-webhook checkout
```

## Manual Steps

If you prefer to set up the webhook manually:

1. **Update the webhook**

```bash
npm run update-webhook
```

2. **Deploy the Supabase function**

```bash
supabase functions deploy stripe-webhook
```

3. **Verify the webhook**

Go to the Stripe Dashboard > Developers > Webhooks and verify that the webhook is properly configured with the following URL:

```
https://pajqstbgncpbpcaffbpm.supabase.co/functions/v1/stripe-webhook
```

## Troubleshooting

If you encounter issues with the webhook:

1. **Check the logs**

```bash
supabase functions logs stripe
```

2. **Verify the webhook secret**

Make sure the `STRIPE_WEBHOOK_SECRET` in your environment matches the one in the Stripe Dashboard.

3. **Test with a specific event**

```bash
stripe trigger checkout.session.completed
```

## Important Notes

- The webhook secret is only shown once when created. If you lose it, you'll need to create a new webhook.
- Make sure your Supabase function has the necessary permissions to access the database.
- The webhook URL must be publicly accessible.
- The webhook endpoint does not require authentication as it uses the webhook secret for verification.
- Regular API calls to the Stripe function still require authentication. 