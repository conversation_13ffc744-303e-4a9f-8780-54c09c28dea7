/**
 * Content script fix to prevent MutationObserver errors from external scripts
 * This script patches common issues with browser extensions and external content scripts
 */

(function() {
  'use strict';

  // Patch MutationObserver to handle invalid node parameters
  const originalObserve = MutationObserver.prototype.observe;
  
  MutationObserver.prototype.observe = function(target, options) {
    try {
      // Check if target is a valid Node
      if (!target || typeof target.nodeType !== 'number') {
        console.warn('MutationObserver.observe called with invalid target:', target);
        return;
      }
      
      // Call original observe method
      return originalObserve.call(this, target, options);
    } catch (error) {
      console.warn('MutationObserver.observe error caught and handled:', error);
    }
  };

  // Patch console methods to reduce noise from external scripts
  const originalConsoleLog = console.log;
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;

  // Filter out known noisy external script messages
  const noisyPatterns = [
    /solanaActionsContentScript/,
    /React DevTools/,
    /Download the React DevTools/,
    /PHANTOM.*error updating cache/,
    /Could not establish connection/
  ];

  function shouldFilterMessage(message) {
    if (typeof message !== 'string') return false;
    return noisyPatterns.some(pattern => pattern.test(message));
  }

  // Only filter in production or when specifically requested
  const shouldFilter = window.location.hostname !== 'localhost' || window.FILTER_CONSOLE_NOISE;

  console.log = function(...args) {
    if (!shouldFilter || !shouldFilterMessage(args[0])) {
      originalConsoleLog.apply(console, args);
    }
  };

  console.warn = function(...args) {
    if (!shouldFilter || !shouldFilterMessage(args[0])) {
      originalConsoleWarn.apply(console, args);
    }
  };

  console.error = function(...args) {
    if (!shouldFilter || !shouldFilterMessage(args[0])) {
      originalConsoleError.apply(console, args);
    }
  };

  // Add global error handler for uncaught errors from external scripts
  window.addEventListener('error', function(event) {
    if (event.filename && event.filename.includes('solanaActionsContentScript')) {
      event.preventDefault();
      console.warn('Prevented Solana content script error:', event.error);
      return false;
    }
  });

  // Add unhandled promise rejection handler
  window.addEventListener('unhandledrejection', function(event) {
    if (event.reason && event.reason.toString().includes('solanaActionsContentScript')) {
      event.preventDefault();
      console.warn('Prevented Solana content script promise rejection:', event.reason);
      return false;
    }
  });

  console.log('Content script error prevention loaded');
})();
