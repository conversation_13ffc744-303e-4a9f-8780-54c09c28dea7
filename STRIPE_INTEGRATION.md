# Stripe Integration Summary

This document summarizes the changes made to properly integrate Stripe subscriptions with the application.

## Overview

The integration ensures that when a user purchases a subscription plan, the system:
1. <PERSON><PERSON><PERSON> updates their subscription status in the database
2. Resets their message tokens
3. Updates the UI to reflect their new plan and message limits

## Key Components

### 1. Stripe Webhook

The webhook is configured to listen for the following events:
- `checkout.session.completed`: When a user completes a checkout session
- `customer.subscription.created`: When a new subscription is created
- `customer.subscription.updated`: When a subscription is updated
- `customer.subscription.deleted`: When a subscription is deleted

### 2. Supabase Function

The Stripe webhook function in Supabase handles webhook events and updates the database accordingly:
- Updates the subscription record in the `subscriptions` table
- Updates the user's profile in the `profiles` table with the new subscription type
- Resets the user's token usage

### 3. Frontend Components

The following components have been updated to properly handle subscriptions:

#### SubscribeButton

- Creates a Stripe customer linked to the user before redirecting to checkout
- Ensures the user's ID is included in the customer metadata

#### SubscriptionSuccessRedirect

- Handles the redirect after a successful payment
- Makes multiple attempts to fetch the subscription data
- Updates the user's profile with the new subscription type
- Resets the user's token usage

#### ChatInterface

- Checks for subscription changes when the component mounts
- Updates the user's profile if the subscription has changed
- Resets the user's token usage when the subscription changes
- Displays the correct number of messages based on the subscription plan

#### ManageSubscription

- Displays the user's current subscription status
- Shows the number of messages remaining
- Provides a manual sync button to force a refresh of subscription data

## Setup Scripts

Several scripts have been created to help with the setup and testing of the Stripe integration:

- `update-webhook.js`: Updates the Stripe webhook endpoint
- `deploy-stripe-function.js`: Deploys the Stripe webhook function to Supabase
- `test-webhook.js`: Tests the webhook locally
- `trigger-webhook-event.js`: Triggers a test webhook event
- `verify-subscription.js`: Verifies the entire subscription system
- `sync-user-subscription.js`: Manually syncs a specific user's subscription

### Verification Script

The verification script (`npm run verify-subscription`) performs a comprehensive check of the Stripe integration:

1. Checks that all required environment variables are present
2. Verifies the connection to Stripe
3. Lists all active products and prices in Stripe
4. Checks the webhook configuration
5. Verifies that the Supabase function is deployed
6. Checks that all required database tables exist

This script is useful for:
- Initial setup verification
- Troubleshooting subscription issues
- Ensuring all components are properly configured after updates

### User Sync Script

The user sync script (`npm run sync-user`) allows you to manually sync a specific user's subscription data:

1. Prompts for a user email or ID
2. Looks up the user in the database
3. Finds the associated Stripe customer
4. Retrieves the latest subscription from Stripe
5. Updates the subscription record in the database
6. Updates the user's profile with the correct subscription type
7. Resets the user's token usage

This script is useful for:
- Fixing issues when a webhook event was missed or failed to process
- Manually updating a user's subscription status
- Troubleshooting subscription issues for specific users

## Troubleshooting

If a user reports issues with their subscription:

1. Run the verification script to check the overall system:
   ```bash
   npm run verify-subscription
   ```

2. Check the Supabase logs for any errors:
   ```bash
   supabase functions logs stripe-webhook
   ```

3. Verify that the webhook is properly configured:
   ```bash
   npm run update-webhook
   ```

4. Use the manual sync button in the ManageSubscription page to force a refresh of subscription data.

5. If the above steps don't resolve the issue, manually sync the user's subscription:
   ```bash
   npm run sync-user
   ```
   Then enter the user's email or ID when prompted.

6. If all else fails, the user can use the "Reset Tokens Manually" button in the subscription limit dialog.

## Future Improvements

1. Add more detailed logging to the Stripe function
2. Implement better error handling for webhook events
3. Add a subscription history view for users
4. Implement prorated upgrades and downgrades 