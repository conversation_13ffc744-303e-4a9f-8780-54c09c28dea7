/* Using SF Pro system font */

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 2%;
    --foreground: 0 0% 98%;

    --card: 0 0% 9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 13%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 13%;
    --muted-foreground: 0 0% 63.9%;

    --accent: 0 0% 13%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 13%;
    --input: 0 0% 13%;
    --ring: 0 0% 83.9%;

    --sidebar: 0 0% 8%;
    --sidebar-foreground: 0 0% 98%;

    --radius: 0.5rem;
  }

  @keyframes border-glow {
    0% {
      background-position: 0% 50%;
    }
    100% {
      background-position: 400% 50%;
    }
  }

  .animate-border-trace {
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
  }

  .animate-border-trace::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(
      90deg,
      transparent,
      transparent,
      rgba(255, 255, 255, 0.9),
      transparent,
      transparent
    );
    background-size: 400% 100%;
    border-radius: inherit;
    z-index: -1;
    animation: border-glow 3s linear infinite;
    mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    mask-composite: xor;
    -webkit-mask:
      linear-gradient(#000 0 0) content-box,
      linear-gradient(#000 0 0);
    -webkit-mask-composite: xor;
    padding: 1px;
  }
}

@layer base {
  * {
    @apply border-[#1A1A1A];
  }
  body {
    @apply bg-[#0A0A0A] text-white antialiased;
    font-family: '-apple-system', 'BlinkMacSystemFont', 'SF Pro Display', 'SF Pro Text', system-ui, sans-serif;
  }
}

.screenshot-visible {
  opacity: 1; /* Make it visible in the screenshot */
}

@media screen {
  .screenshot-visible {
      opacity: 0; /* Hide it in the UI */
  }
}

.glass-morphism {
  @apply backdrop-blur-xl bg-white/5 shadow-[0_4px_12px_-2px_rgba(0,0,0,0.3)];
}

.neo-blur {
  @apply backdrop-blur-2xl bg-black/40;
}

.web-search-card {
  @apply rounded-2xl border border-white/10 bg-[#141414]/80 backdrop-blur-md max-w-4xl mx-auto w-full;
}

.web-search-result {
  @apply p-4 rounded-xl border border-white/5 bg-[#1A1A1A] hover:bg-[#1A1A1A]/80 transition-colors;
}

.message-user {
  @apply text-white w-full max-w-4xl mx-auto;
}

.message-ai {
  @apply text-white/90 w-full max-w-4xl mx-auto;
}

.search-status {
  @apply flex items-center gap-2 text-sm text-white/60;
}

.search-query {
  @apply px-4 py-2 rounded-full bg-[#1A1A1A] border border-white/10 text-sm text-white/90 hover:bg-[#1A1A1A]/80 transition-colors;
}

.results-count {
  @apply bg-[#1A1A1A] px-3 py-1 rounded-full text-xs text-white/60 border border-white/10;
}

.loading-dots:after {
  content: '.';
  animation: dots 1.5s steps(5, end) infinite;
}

@keyframes dots {
  0%, 20% { content: '.'; }
  40% { content: '..'; }
  60% { content: '...'; }
  80% { content: '....'; }
  100% { content: ''; }
}

.search-animation {
  @apply relative overflow-hidden;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out forwards;
}

/* For message cards/containers */
.message-card {
  @apply border border-white/10 bg-[#141414] rounded-lg;
}

/* For the chat container */
.chat-container {
  @apply border-white/70; /* Slightly lighter border */
}


.prose code {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.2em 0.4em;
  border-radius: 0.25em;
  font-size: 0.875em;
}

/* Simple clean carousel styling */
.carousel-container {
  display: flex;
  overflow-x: auto;
  gap: 8px;
  width: 100%;
  padding: 4px 2px;
  /* Remove any scroll behavior modifiers */
}

/* Simple clean card styling */
.search-result-item {
  flex: 0 0 240px !important;
  min-width: 240px !important;
  width: 240px !important;
  height: 120px !important; /* Reduced height */
  background-color: rgba(30, 30, 30, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.08);
  border-radius: 10px;
  padding: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.15s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.animate-pop-in {
  animation: none; /* Disable pop-in animation */
}

.auto-scroll-active .search-result-item {
  /* No special styles */
}

.auto-scroll-active .search-result-item:hover {
  /* No transform or glow effects */
}

@media (prefers-reduced-motion: no-preference) {
  .carousel-container {
    scroll-behavior: smooth;
  }
}

.hide-scrollbar::-webkit-scrollbar {
  width: 0px;
  height: 0px;
  background: transparent;
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

@keyframes slight-glow {
  0%, 100% { box-shadow: none; }
  50% { box-shadow: none; }
}

/* Enhanced carousel outer container with modern styling */
.carousel-outer-container {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 8px;
  padding: 12px 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px -6px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin: 0;
}

/* Remove the divider above the section */
.response-section::before {
  display: none;
}

/* Make section spacing tighter */
.response-section {
  margin-bottom: 16px;
}

/* Styling for clickable card links */
.search-result-item-link {
  text-decoration: none;
  color: inherit;
  display: block;
  flex: 0 0 240px !important;
  min-width: 240px !important;
  width: 240px !important;
  height: 120px !important;
  position: relative;
  cursor: pointer;
  margin: 0; /* Remove any margin that might affect alignment */
}

/* Subtle hover effect to indicate interactivity */
.search-result-item-link:hover .search-result-item {
  transform: translateY(-2px);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Add a subtle indicator for clickable items */
.search-result-item-link .search-result-item::after {
  content: '↗';
  position: absolute;
  top: 12px;
  right: 12px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.4);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.search-result-item-link:hover .search-result-item::after {
  opacity: 1;
}

/* Smaller font sizes and tighter spacing */
.search-result-item h3 {
  font-size: 12px;
  line-height: 1.2;
  margin-bottom: 4px;
}

.search-result-item p {
  font-size: 11px;
  line-height: 1.3;
}

.search-result-item .flex.items-center {
  height: 16px;
  margin-bottom: 6px;
}

/* Make the header more compact */
.response-section .flex.items-center.gap-2.mb-3 {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px; /* Make sure gap is consistent */
}

.response-section .text-md {
  font-size: 14px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  letter-spacing: 0.01em;
}

.response-section .text-sm {
  font-size: 12px;
}

/* Harmonize the container styling for consistency */
.carousel-outer-container,
.ai-content-container {
  background: rgba(20, 20, 20, 0.5);
  border-radius: 8px;
  padding: 12px 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px -6px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.05);
  margin: 0;
}

/* Sophisticated Block and Connection Animations */
@keyframes subtle-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.08);
  }
  50% {
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0);
  }
}

@keyframes wave-flow {
  0% {
    stroke-dashoffset: 10;
  }
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes subtle-highlight {
  0% {
    border-color: rgba(16, 185, 129, 0.15);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }
  100% {
    border-color: rgba(16, 185, 129, 0.25);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(16, 185, 129, 0.1);
  }
}

/* React Flow Edge Animations - Subtle Wave Effect */
.react-flow__edge-path {
  transition: all 0.4s ease;
}

.react-flow__edge.animated .react-flow__edge-path {
  stroke-dasharray: 6,4;
  animation: wave-flow 5s linear infinite;
}

.react-flow__edge:hover .react-flow__edge-path {
  stroke: rgba(16, 185, 129, 0.6);
  stroke-width: 2;
  filter: drop-shadow(0 1px 3px rgba(16, 185, 129, 0.2));
}

/* Refined Handle Styling */
.react-flow__handle {
  width: 12px !important;
  height: 12px !important;
  border: 2px solid white !important;
  border-radius: 50% !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
  /* Remove the background override to allow individual handle colors */
}

.react-flow__handle:hover {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.4), 0 0 8px rgba(255, 255, 255, 0.2) !important;
  transform: scale(1.2) !important;
  cursor: crosshair !important;
  z-index: 20 !important;
}

.react-flow__handle-connecting {
  animation: connection-pulse 1.5s infinite !important;
  border-color: rgba(16, 185, 129, 0.8) !important;
  background: rgba(16, 185, 129, 0.6) !important;
  transform: scale(1.2) !important;
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.6) !important;
}

/* Enhanced connection feedback */
.react-flow__handle.react-flow__handle-valid {
  background: rgba(34, 197, 94, 0.9) !important;
  border-color: rgba(34, 197, 94, 0.7) !important;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.5) !important;
  animation: valid-target-pulse 1s infinite !important;
}

.react-flow__handle.react-flow__handle-invalid {
  background: rgba(239, 68, 68, 0.9) !important;
  border-color: rgba(239, 68, 68, 0.7) !important;
  box-shadow: 0 0 8px rgba(239, 68, 68, 0.5) !important;
}

@keyframes connection-pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 0 16px rgba(16, 185, 129, 0.8);
  }
}

@keyframes valid-target-pulse {
  0%, 100% {
    transform: scale(1.2);
    box-shadow: 0 0 8px rgba(34, 197, 94, 0.5);
  }
  50% {
    transform: scale(1.4);
    box-shadow: 0 0 12px rgba(34, 197, 94, 0.8);
  }
}

/* Enhanced visibility during connection mode */
.react-flow__handle.connecting-mode {
  background: rgba(16, 185, 129, 0.8) !important;
  border-color: rgba(16, 185, 129, 0.6) !important;
  transform: scale(1.1) !important;
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.4) !important;
  z-index: 30 !important;
}

/* Better connection line visibility */
.react-flow__connectionline {
  stroke: rgba(16, 185, 129, 0.8) !important;
  stroke-width: 3px !important;
  stroke-linecap: round !important;
  stroke-dasharray: 8,4 !important;
  filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3)) !important;
  z-index: 100 !important;
}

/* AI Backtesting Experience Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}

@keyframes float-up {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(59, 130, 246, 0.3);
  }
}

@keyframes data-stream {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes matrix-rain {
  0% {
    transform: translateY(-100vh);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(100vh);
    opacity: 0;
  }
}

@keyframes hologram-flicker {
  0%, 100% {
    opacity: 1;
    filter: hue-rotate(0deg);
  }
  25% {
    opacity: 0.8;
    filter: hue-rotate(90deg);
  }
  50% {
    opacity: 0.9;
    filter: hue-rotate(180deg);
  }
  75% {
    opacity: 0.85;
    filter: hue-rotate(270deg);
  }
}

@keyframes neural-pulse {
  0% {
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  }
  50% {
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.3) 0%, transparent 70%);
  }
  100% {
    background: radial-gradient(circle at center, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-up {
  animation: float-up 0.8s ease-out forwards;
}

.animate-glow-pulse {
  animation: glow-pulse 3s ease-in-out infinite;
}

.animate-data-stream {
  animation: data-stream 2s linear infinite;
}

.animate-matrix-rain {
  animation: matrix-rain 4s linear infinite;
}

.animate-hologram-flicker {
  animation: hologram-flicker 4s ease-in-out infinite;
}

.animate-neural-pulse {
  animation: neural-pulse 2s ease-in-out infinite;
}

/* Scrollbar hiding for clean look */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Subtle Block Selection Enhancement */
.react-flow__node.selected {
  animation: subtle-highlight 0.5s ease-out forwards;
}

/* Smooth Block Transitions */
.react-flow__node {
  transition: all 0.4s ease;
}

/* Connection Line Styling - Subtle Wave Theme */
.react-flow__connectionline {
  stroke: rgba(16, 185, 129, 0.5) !important;
  stroke-width: 1.5px !important;
  stroke-linecap: round !important;
  stroke-dasharray: 6,4 !important;
  filter: drop-shadow(0 1px 2px rgba(16, 185, 129, 0.15)) !important;
}

/* Background Grid Enhancement */
.react-flow__background {
  background-color: #0A0A0A;
}

.react-flow__background .react-flow__background-pattern {
  stroke: rgba(255, 255, 255, 0.03);
  stroke-width: 1;
}

/* Minimap Styling */
.react-flow__minimap {
  background-color: rgba(26, 26, 26, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
}

/* Controls Styling */
.react-flow__controls {
  background: rgba(26, 26, 26, 0.8) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 8px !important;
}

.react-flow__controls-button {
  background: rgba(255, 255, 255, 0.05) !important;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: rgba(255, 255, 255, 0.7) !important;
  transition: all 0.2s ease !important;
}

.react-flow__controls-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
}

/* Give the AI content container slightly different padding for text content */
.ai-content-container {
  padding: 16px;
}

/* Ensure consistent icon styling */
.response-section .h-5.w-5,
.ai-section-icon,
.response-section svg {
  width: 20px !important;
  height: 20px !important;
  color: rgba(255, 255, 255, 0.6) !important;
}

/* Simplified AI content styling */
.ai-content {
  font-size: 14px;
  line-height: 1.4;
  color: rgba(255, 255, 255, 0.8);
}

.ai-content p {
  margin: 0.5em 0;
}

.ai-content br {
  display: block;
  margin: 0.2em 0;
  content: "";
}

.ai-content ul, .ai-content ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.ai-content li {
  margin: 0.2em 0;
}

.ai-content h1, .ai-content h2, .ai-content h3 {
  margin: 1em 0 0.4em;
}


.ai-content strong {
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
}

/* You can also add this utility class and apply it where needed */
.border-subtle {
  @apply border border-white/15 !important; /* Slightly lighter border that overrides other styles */
}

/* Falling bright animation for loading dots */
@keyframes falling-bright {
  0% {
    opacity: 0.3;
    transform: translateY(-2px);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
  50% {
    opacity: 1;
    transform: translateY(0px);
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
  }
  100% {
    opacity: 0.3;
    transform: translateY(2px);
    box-shadow: 0 0 0 rgba(255, 255, 255, 0);
  }
}

.animate-falling-bright {
  animation: falling-bright 2s ease-in-out infinite;
}

/* Border worm animation for buttons */
@keyframes border-worm {
  0% {
    transform: rotate(0deg) scale(1);
    opacity: 0.6;
  }
  25% {
    transform: rotate(90deg) scale(1.05);
    opacity: 0.8;
  }
  50% {
    transform: rotate(180deg) scale(1);
    opacity: 1;
  }
  75% {
    transform: rotate(270deg) scale(1.05);
    opacity: 0.8;
  }
  100% {
    transform: rotate(360deg) scale(1);
    opacity: 0.6;
  }
}

.animate-border-worm {
  animation: border-worm 3s linear infinite;
}

/* Custom dashed line with better spacing for chat interface */
.chat-dashed-line {
  border-left-width: 2px;
  border-left-style: dashed;
  border-left-color: rgba(255, 255, 255, 0.7);
  border-image: none;
}

/* Clean dropdown animations */
@keyframes dropdown-slide-in {
  from {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdown-slide-out {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-8px) scale(0.95);
  }
}

/* Enhanced dropdown styling */
[data-radix-select-content],
[data-radix-dropdown-menu-content] {
  animation-duration: 200ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}

[data-radix-select-content][data-state="open"],
[data-radix-dropdown-menu-content][data-state="open"] {
  animation-name: dropdown-slide-in;
}

[data-radix-select-content][data-state="closed"],
[data-radix-dropdown-menu-content][data-state="closed"] {
  animation-name: dropdown-slide-out;
}
