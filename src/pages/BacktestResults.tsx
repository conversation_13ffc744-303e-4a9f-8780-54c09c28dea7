import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { X, TrendingUp, Target, BarChart3, Zap, ArrowLeft } from 'lucide-react';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { fetchTradeChartData, adjustTradeToMarketOpen } from '@/services/cleanChartService';
import { useToast } from '@/components/ui/use-toast';

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const BacktestResults: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const backtestResult = location.state?.backtestResult as BacktestResult;
  const agentName = location.state?.agentName as string;
  const selectedSymbol = location.state?.selectedSymbol as string;
  
  const [isAnimating, setIsAnimating] = useState(true);
  const [currentTradeIndex, setCurrentTradeIndex] = useState<number | undefined>(undefined);
  const [mainChartData, setMainChartData] = useState<any[]>([]);
  const [isLoadingMainChart, setIsLoadingMainChart] = useState(false);
  const [mainChartTimeframe, setMainChartTimeframe] = useState<'1min' | '5min' | '15min' | '30min' | '1hour' | '4hour' | 'daily'>('1hour');

  // Chart timeframe options
  const chartTimeframeOptions = [
    { value: '1min' as const, label: '1m', description: '1 minute' },
    { value: '5min' as const, label: '5m', description: '5 minutes' },
    { value: '15min' as const, label: '15m', description: '15 minutes' },
    { value: '30min' as const, label: '30m', description: '30 minutes' },
    { value: '1hour' as const, label: '1h', description: '1 hour' },
    { value: '4hour' as const, label: '4h', description: '4 hours' },
    { value: 'daily' as const, label: '1D', description: '1 day' }
  ];

  // Animation sequence
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  // Handle trade click
  const handleTradeClick = async (tradeIndex: number) => {
    if (!backtestResult?.trades || !backtestResult.trades[tradeIndex]) return;
    
    setCurrentTradeIndex(tradeIndex);
    setIsLoadingMainChart(true);
    
    try {
      const selectedTrade = backtestResult.trades[tradeIndex];
      const tradeTimestamp = new Date(selectedTrade.date).getTime();
      const adjustedTimestamp = adjustTradeToMarketOpen(tradeTimestamp);
      
      const chartData = await fetchTradeChartData(
        selectedSymbol,
        adjustedTimestamp,
        mainChartTimeframe
      );
      
      setMainChartData(chartData);
    } catch (error) {
      console.error('Error loading chart data:', error);
      toast({
        title: 'Chart Error',
        description: 'Failed to load chart data for this trade',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingMainChart(false);
    }
  };

  // Load chart data when timeframe changes
  useEffect(() => {
    if (currentTradeIndex !== undefined && backtestResult?.trades) {
      handleTradeClick(currentTradeIndex);
    }
  }, [mainChartTimeframe]);

  // Handle back navigation
  const handleBack = () => {
    navigate(-1);
  };

  if (!backtestResult) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-center">
          <div className="text-white/60 text-lg mb-4">No backtest results found</div>
          <button
            onClick={handleBack}
            className="px-6 py-3 bg-white text-black rounded-lg hover:bg-gray-200 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white overflow-hidden relative">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/10 via-purple-900/5 to-green-900/10 animate-pulse" />
      
      {/* Floating Particles Animation */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1 h-1 bg-blue-400/30 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${3 + Math.random() * 4}s`
            }}
          />
        ))}
      </div>

      {/* Header with Exit Button */}
      <div className={`relative z-50 flex items-center justify-between p-6 border-b border-white/[0.08] backdrop-blur-sm bg-[#0A0A0A]/80 transition-all duration-1000 ${
        isAnimating ? 'translate-y-[-100%] opacity-0' : 'translate-y-0 opacity-100'
      }`}>
        <div className="flex items-center gap-4">
          <button
            onClick={handleBack}
            className="p-2 rounded-lg bg-white/[0.05] hover:bg-white/[0.1] border border-white/[0.1] transition-all duration-300 group"
          >
            <ArrowLeft className="h-5 w-5 text-white/70 group-hover:text-white transition-colors" />
          </button>
          <div>
            <h1 className="text-2xl font-bold bg-gradient-to-r from-white via-blue-200 to-green-200 bg-clip-text text-transparent">
              AI Backtest Results
            </h1>
            <p className="text-white/60 text-sm">
              {agentName} • {selectedSymbol} • {backtestResult.numberOfTrades} trades analyzed
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="px-4 py-2 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg border border-blue-500/30">
            <span className="text-sm text-blue-300">Live Analysis</span>
          </div>
          <button
            onClick={handleBack}
            className="p-2 rounded-lg bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 transition-all duration-300 group"
          >
            <X className="h-5 w-5 text-red-300 group-hover:text-red-200 transition-colors" />
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className={`relative z-10 h-[calc(100vh-89px)] transition-all duration-1500 delay-300 ${
        isAnimating ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
      }`}>
        
        {/* Performance Metrics - Floating Cards */}
        <div className={`absolute top-6 left-6 right-6 z-20 transition-all duration-1000 delay-500 ${
          isAnimating ? 'translate-y-[-50px] opacity-0' : 'translate-y-0 opacity-100'
        }`}>
          <div className="grid grid-cols-4 gap-4">
            {[
              {
                icon: TrendingUp,
                label: 'Total Return',
                value: `${backtestResult.totalReturn > 0 ? '+' : ''}${backtestResult.totalReturn.toFixed(2)}%`,
                color: backtestResult.totalReturn > 0 ? 'emerald' : 'red',
                glow: backtestResult.totalReturn > 0 ? 'emerald' : 'red'
              },
              {
                icon: BarChart3,
                label: 'Total Trades',
                value: backtestResult.numberOfTrades.toString(),
                color: 'blue',
                glow: 'blue'
              },
              {
                icon: Target,
                label: 'Win Rate',
                value: `${backtestResult.winRate.toFixed(1)}%`,
                color: 'emerald',
                glow: 'emerald'
              },
              {
                icon: Zap,
                label: 'Max Drawdown',
                value: `-${backtestResult.maxDrawdown.toFixed(2)}%`,
                color: 'red',
                glow: 'red'
              }
            ].map((metric, index) => (
              <div
                key={metric.label}
                className={`bg-[#0D0D0D]/80 backdrop-blur-sm border border-white/[0.08] rounded-xl p-4 shadow-[0_0_20px_rgba(59,130,246,0.1)] hover:shadow-[0_0_30px_rgba(59,130,246,0.2)] transition-all duration-500 group animate-float-up`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className={`p-2 rounded-lg bg-${metric.color}-500/20 group-hover:bg-${metric.color}-500/30 transition-colors`}>
                    <metric.icon className={`h-4 w-4 text-${metric.color}-400`} />
                  </div>
                  <span className="text-sm text-white/70">{metric.label}</span>
                </div>
                <div className={`text-2xl font-bold text-${metric.color}-400 group-hover:scale-105 transition-transform`}>
                  {metric.value}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Main Chart Area */}
        <div className={`absolute top-28 left-6 right-80 bottom-6 transition-all duration-1200 delay-700 ${
          isAnimating ? 'scale-90 opacity-0' : 'scale-100 opacity-100'
        }`}>
          <div className="h-full bg-[#0D0D0D]/80 backdrop-blur-sm border border-white/[0.08] rounded-xl overflow-hidden shadow-[0_0_40px_rgba(59,130,246,0.15)] ring-1 ring-blue-500/20 animate-glow-pulse">

            {/* Chart Header */}
            <div className="flex items-center justify-between p-4 border-b border-white/[0.08] bg-gradient-to-r from-[#0D0D0D] to-[#111111]">
              <div>
                <h3 className="text-xl font-bold bg-gradient-to-r from-white via-blue-200 to-green-200 bg-clip-text text-transparent flex items-center gap-2">
                  🚀 AI Trading Analysis
                  {currentTradeIndex !== undefined && (
                    <span className="text-xs bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full animate-pulse">
                      Trade {currentTradeIndex + 1}/{backtestResult.trades.length}
                    </span>
                  )}
                </h3>
                <p className="text-white/60 text-sm">
                  {currentTradeIndex !== undefined
                    ? `${selectedSymbol} - ${mainChartTimeframe} intervals with precision markers`
                    : 'Select a trade from the history to dive into detailed market analysis'
                  }
                </p>
              </div>

              {/* Timeframe Controls */}
              <div className="flex items-center gap-3">
                <div className="flex items-center gap-2 text-white/60 text-sm">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>Live Data</span>
                </div>
                <div className="flex items-center gap-1 bg-[#0F0F11] rounded-lg p-1 border border-[#1A1A1C] shadow-inner">
                  {chartTimeframeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setMainChartTimeframe(option.value)}
                      disabled={isLoadingMainChart}
                      className={`px-3 py-2 text-xs font-medium rounded transition-all duration-300 ${
                        mainChartTimeframe === option.value
                          ? 'bg-blue-500 text-white shadow-lg shadow-blue-500/30 scale-105'
                          : 'text-white/60 hover:text-white hover:bg-white/5 hover:scale-105'
                      } ${isLoadingMainChart ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      title={option.description}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Chart Content */}
            <div className="relative bg-[#0A0A0A] h-[calc(100%-73px)]">
              {currentTradeIndex !== undefined && mainChartData.length > 0 ? (
                <CleanCandlestickChart
                  symbol={selectedSymbol}
                  data={mainChartData}
                  tradeMarkers={[
                    {
                      timestamp: adjustTradeToMarketOpen(new Date(backtestResult.trades[currentTradeIndex].date).getTime()),
                      price: backtestResult.trades[currentTradeIndex].price,
                      type: backtestResult.trades[currentTradeIndex].type === 'buy' ? 'entry' : 'exit',
                      label: `${backtestResult.trades[currentTradeIndex].type.toUpperCase()} $${backtestResult.trades[currentTradeIndex].price.toFixed(2)}`,
                      color: backtestResult.trades[currentTradeIndex].type === 'buy' ? '#10b981' : '#ef4444',
                      tradeIndex: currentTradeIndex
                    }
                  ]}
                  height={600}
                  className="w-full h-full"
                  currentTradeIndex={currentTradeIndex}
                  allTrades={backtestResult.trades}
                />
              ) : isLoadingMainChart ? (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="relative">
                      <div className="h-16 w-16 border-4 border-white/10 border-t-blue-400 rounded-full animate-spin mb-6 mx-auto"></div>
                      <div className="absolute inset-0 h-16 w-16 border-4 border-transparent border-r-green-400 rounded-full animate-spin mx-auto" style={{ animationDirection: 'reverse', animationDuration: '1.5s' }}></div>
                    </div>
                    <div className="text-white/70 text-xl mb-3 animate-pulse">Analyzing {mainChartTimeframe} market data...</div>
                    <div className="text-white/50 text-sm mb-2">Processing candlestick patterns and trade signals</div>
                    <div className="text-white/30 text-xs">This may take a few moments for optimal precision</div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center max-w-2xl animate-hologram-flicker">
                    <div className="text-6xl mb-6 animate-float">🎯</div>
                    <div className="text-white/70 text-2xl mb-4 font-bold">AI-Powered Market Analysis</div>
                    <div className="text-white/50 text-lg mb-6">Select any trade from the history to unlock detailed insights</div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-white/40">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                        <span>Interactive zoom & pan controls</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span>Detailed OHLC hover data</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                        <span>Precise trade entry/exit markers</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                        <span>Multiple timeframe analysis</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Trade History Sidebar */}
        <div className={`absolute top-28 right-6 bottom-6 w-72 transition-all duration-1000 delay-900 ${
          isAnimating ? 'translate-x-[100%] opacity-0' : 'translate-x-0 opacity-100'
        }`}>
          <div className="h-full bg-[#0D0D0D]/80 backdrop-blur-sm border border-white/[0.08] rounded-xl overflow-hidden shadow-[0_0_20px_rgba(59,130,246,0.1)] ring-1 ring-white/5">

            {/* Sidebar Header */}
            <div className="p-4 border-b border-white/[0.08] bg-gradient-to-r from-[#0D0D0D] to-[#111111]">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-3 h-3 bg-gradient-to-r from-blue-400 to-green-400 rounded-full animate-pulse"></div>
                <h3 className="text-lg font-semibold text-white">Trade Timeline</h3>
              </div>
              <p className="text-sm text-white/60">Click any trade to analyze market conditions</p>
              <div className="flex items-center gap-2 mt-2 text-xs text-white/50">
                <span>{backtestResult.trades.length} total executions</span>
                <span>•</span>
                <span>{backtestResult.winRate.toFixed(1)}% success rate</span>
              </div>
            </div>

            {/* Trade List */}
            <div className="flex-1 overflow-y-auto scrollbar-hide">
              <div className="p-3">
                <div className="space-y-2">
                  {backtestResult.trades.slice().reverse().map((trade, reversedIndex) => {
                    const originalIndex = backtestResult.trades.length - 1 - reversedIndex;
                    const isSelected = currentTradeIndex === originalIndex;
                    const isWinningTrade = trade.type === 'sell' && reversedIndex > 0 &&
                      backtestResult.trades[backtestResult.trades.length - reversedIndex].type === 'buy' &&
                      trade.price > backtestResult.trades[backtestResult.trades.length - reversedIndex].price;

                    return (
                      <div
                        key={reversedIndex}
                        className={`relative p-3 rounded-lg transition-all duration-300 cursor-pointer border group ${
                          isSelected
                            ? 'bg-blue-500/20 border-blue-500/40 shadow-[0_0_20px_rgba(59,130,246,0.3)] scale-105'
                            : 'hover:bg-white/[0.02] border-white/[0.06] hover:border-white/[0.12] hover:scale-102'
                        }`}
                        onClick={() => handleTradeClick(originalIndex)}
                      >
                        {/* Trade Type Indicator */}
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${
                              trade.type === 'buy' ? 'bg-emerald-400 shadow-[0_0_8px_rgba(16,185,129,0.5)]' : 'bg-red-400 shadow-[0_0_8px_rgba(239,68,68,0.5)]'
                            } ${isSelected ? 'animate-pulse' : ''}`} />
                            <span className={`text-sm font-bold ${
                              trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                            }`}>
                              {trade.type.toUpperCase()}
                            </span>
                            {isWinningTrade && (
                              <div className="text-xs bg-green-500/20 text-green-300 px-2 py-0.5 rounded-full">
                                WIN
                              </div>
                            )}
                          </div>
                          <span className="text-sm text-white font-mono font-bold">${trade.price.toFixed(2)}</span>
                        </div>

                        {/* Trade Details */}
                        <div className="space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-white/60 font-mono">{trade.date}</span>
                            <div className="flex items-center gap-1">
                              <div className={`w-1.5 h-1.5 rounded-full ${
                                trade.confidence >= 80 ? 'bg-green-400' :
                                trade.confidence >= 60 ? 'bg-yellow-400' : 'bg-red-400'
                              }`} />
                              <span className="text-white/70 font-medium">{trade.confidence}%</span>
                            </div>
                          </div>
                          <div className="text-xs text-white/50 truncate">{trade.signal}</div>
                        </div>

                        {/* Selection Indicator */}
                        {isSelected && (
                          <div className="absolute inset-0 rounded-lg border-2 border-blue-400/50 pointer-events-none animate-pulse">
                            <div className="absolute top-1 right-1 w-2 h-2 bg-blue-400 rounded-full animate-ping"></div>
                          </div>
                        )}

                        {/* Hover Effect */}
                        <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-blue-500/5 to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Footer Stats */}
            <div className="p-4 border-t border-white/[0.08] bg-gradient-to-r from-[#0D0D0D] to-[#111111]">
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="text-center">
                  <div className="text-emerald-400 font-bold text-sm">
                    {Math.round(backtestResult.trades.filter(t => t.type === 'buy').length)}
                  </div>
                  <div className="text-white/60">Entries</div>
                </div>
                <div className="text-center">
                  <div className="text-red-400 font-bold text-sm">
                    {Math.round(backtestResult.trades.filter(t => t.type === 'sell').length)}
                  </div>
                  <div className="text-white/60">Exits</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BacktestResults;
