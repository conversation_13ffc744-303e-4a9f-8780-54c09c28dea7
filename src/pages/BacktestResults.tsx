import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { X, TrendingUp, Target, BarChart3, Zap, ArrowLeft } from 'lucide-react';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { fetchTradeChartData, adjustTradeToMarketOpen } from '@/services/cleanChartService';
import { useToast } from '@/components/ui/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const BacktestResults: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const backtestResult = location.state?.backtestResult as BacktestResult;
  const agentName = location.state?.agentName as string;
  const selectedSymbol = location.state?.selectedSymbol as string;

  // Handle running backtest from navigation
  const isRunningBacktest = location.state?.isRunningBacktest as boolean;
  const agentId = location.state?.agentId as string;
  const selectedTimeframe = location.state?.selectedTimeframe as string;
  const selectedInterval = location.state?.selectedInterval as string;
  const userId = location.state?.userId as string;
  const currentDate = location.state?.currentDate as string;
  const isMarketplaceAgent = location.state?.isMarketplaceAgent as boolean;
  const agentConfiguration = location.state?.agentConfiguration;
  
  const [isAnimating, setIsAnimating] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [actualBacktestResult, setActualBacktestResult] = useState<BacktestResult | null>(backtestResult);
  const [currentTradeIndex, setCurrentTradeIndex] = useState<number | undefined>(undefined);
  const [mainChartData, setMainChartData] = useState<any[]>([]);
  const [isLoadingMainChart, setIsLoadingMainChart] = useState(false);
  const [mainChartTimeframe, setMainChartTimeframe] = useState<'1min' | '5min' | '15min' | '30min' | '1hour' | '4hour' | 'daily'>('1hour');

  // Chart timeframe options
  const chartTimeframeOptions = [
    { value: '1min' as const, label: '1m', description: '1 minute' },
    { value: '5min' as const, label: '5m', description: '5 minutes' },
    { value: '15min' as const, label: '15m', description: '15 minutes' },
    { value: '30min' as const, label: '30m', description: '30 minutes' },
    { value: '1hour' as const, label: '1h', description: '1 hour' },
    { value: '4hour' as const, label: '4h', description: '4 hours' },
    { value: 'daily' as const, label: '1D', description: '1 day' }
  ];

  // Execute backtest if needed, then handle loading and animation
  useEffect(() => {
    const executeBacktest = async () => {
      if (isRunningBacktest && agentId) {
        try {
          const requestBody = {
            agentId,
            symbol: selectedSymbol,
            timeframe: selectedTimeframe,
            interval: selectedInterval,
            userId,
            currentDate,
            isMarketplaceAgent,
            agentConfiguration
          };

          const { data, error } = await supabase.functions.invoke('agent-backtesting', {
            body: requestBody
          });

          if (error) throw error;

          // Create mock performance chart if missing
          if (!data.performanceChart || data.performanceChart.length === 0) {
            const mockChartData = [];
            const startDate = new Date();
            startDate.setFullYear(startDate.getFullYear() - 1);
            const baseValue = 10000;
            const totalReturnDecimal = data.totalReturn / 100;
            const numberOfMonths = 12;

            for (let month = 0; month < numberOfMonths; month++) {
              const date = new Date(startDate);
              date.setMonth(date.getMonth() + month);
              const progress = month / (numberOfMonths - 1);
              const currentValue = baseValue * (1 + totalReturnDecimal * progress);
              const volatility = Math.sin(month * 0.5) * 0.002;
              const finalValue = currentValue * (1 + volatility);

              mockChartData.push({
                date: date.toISOString().split('T')[0],
                agentValue: finalValue,
                buyHoldValue: baseValue * (1 + 0.1 * progress)
              });
            }

            if (mockChartData.length > 0) {
              const exactFinalValue = baseValue * (1 + totalReturnDecimal);
              mockChartData[mockChartData.length - 1].agentValue = exactFinalValue;
            }

            data.performanceChart = mockChartData;
          }

          setActualBacktestResult(data);
        } catch (error) {
          console.error('Backtest error:', error);
          toast({
            title: 'Backtest Failed',
            description: 'Failed to run backtest. Please try again.',
            variant: 'destructive'
          });
          navigate('/agent-backtesting');
          return;
        }
      }

      // Show loading for shorter time, then start entrance animation
      const loadingTimer = setTimeout(() => {
        setIsLoading(false);
        // Auto-select first trade when loading completes
        if (actualBacktestResult.trades && actualBacktestResult.trades.length > 0) {
          setCurrentTradeIndex(0);
          handleTradeClick(0);
        }
      }, 500);

      const animationTimer = setTimeout(() => {
        setIsAnimating(false);
      }, 800);

      return () => {
        clearTimeout(loadingTimer);
        clearTimeout(animationTimer);
      };
    };

    executeBacktest();
  }, [isRunningBacktest, agentId, selectedSymbol, selectedTimeframe, selectedInterval, userId, currentDate, isMarketplaceAgent, agentConfiguration, toast, navigate]);

  // Handle trade click
  const handleTradeClick = async (tradeIndex: number) => {
    if (!actualBacktestResult?.trades || !actualBacktestResult.trades[tradeIndex]) return;

    setCurrentTradeIndex(tradeIndex);
    setIsLoadingMainChart(true);

    try {
      const selectedTrade = actualBacktestResult.trades[tradeIndex];
      const tradeTimestamp = new Date(selectedTrade.date).getTime();
      const adjustedTimestamp = adjustTradeToMarketOpen(tradeTimestamp);
      
      const chartData = await fetchTradeChartData(
        selectedSymbol,
        adjustedTimestamp,
        mainChartTimeframe
      );
      
      setMainChartData(chartData);
    } catch (error) {
      console.error('Error loading chart data:', error);
      toast({
        title: 'Chart Error',
        description: 'Failed to load chart data for this trade',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingMainChart(false);
    }
  };

  // Load chart data when timeframe changes
  useEffect(() => {
    if (currentTradeIndex !== undefined && actualBacktestResult?.trades) {
      handleTradeClick(currentTradeIndex);
    }
  }, [mainChartTimeframe, actualBacktestResult]);

  // Handle back navigation to main backtesting page
  const handleBack = () => {
    navigate('/agent-backtesting');
  };

  if (!actualBacktestResult && !isRunningBacktest) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-center">
          <div className="text-white/60 text-lg mb-4">No backtest results found</div>
          <button
            onClick={handleBack}
            className="px-6 py-3 bg-white text-black rounded-lg hover:bg-gray-200 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Show loading screen
  if (isLoading || !actualBacktestResult) {
    return (
      <div className="bg-[#0A0A0A] text-white min-h-screen flex items-center justify-center">
        <div className="text-center">
          {/* Simple spinning loader */}
          <div className="w-8 h-8 border-2 border-white/[0.2] border-t-white rounded-full animate-spin mx-auto mb-6"></div>

          <div className="text-white/70 text-lg font-medium mb-2">Analyzing Results</div>
          <div className="text-white/50 text-sm">Preparing your trading analysis...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-[#0A0A0A] text-white overflow-auto" style={{ height: '100vh' }}>
      <div style={{ height: '200vh' }}>
      {/* Subtle Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] via-transparent to-white/[0.01]" />

      {/* Minimal Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-0.5 h-0.5 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Compact Header with Performance Cards */}
      <div className={`relative z-50 transition-all duration-1000 ${
        isAnimating ? 'translate-y-[-100%] opacity-0' : 'translate-y-0 opacity-100'
      }`}>

        {/* Top Navigation Bar */}
        <div className="flex items-center justify-between p-4 border-b border-white/[0.06] bg-[#0A0A0A]/95 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <button
              onClick={handleBack}
              className="p-2 rounded-lg bg-white/[0.05] hover:bg-white/[0.1] border border-white/[0.08] transition-all duration-300 group"
            >
              <ArrowLeft className="h-4 w-4 text-white/70 group-hover:text-white transition-colors" />
            </button>
            <div>
              <h1 className="text-lg font-bold text-white">AI Backtest Results</h1>
              <p className="text-white/60 text-xs">{agentName} • {selectedSymbol}</p>
            </div>
          </div>

          <button
            onClick={handleBack}
            className="p-2 rounded-lg bg-white/[0.05] hover:bg-white/[0.1] border border-white/[0.08] transition-all duration-300 group"
          >
            <X className="h-4 w-4 text-white/70 group-hover:text-white transition-colors" />
          </button>
        </div>

        {/* Performance Metrics Cards - Compact Top Section */}
        <div className={`p-4 bg-[#0A0A0A]/90 backdrop-blur-sm transition-all duration-800 delay-300 ${
          isAnimating ? 'translate-y-[-20px] opacity-0' : 'translate-y-0 opacity-100'
        }`}>
          <div className="grid grid-cols-4 gap-3">
            {[
              {
                icon: TrendingUp,
                label: 'Total Return',
                value: `${actualBacktestResult.totalReturn > 0 ? '+' : ''}${actualBacktestResult.totalReturn.toFixed(2)}%`,
                color: actualBacktestResult.totalReturn > 0 ? 'emerald' : 'red'
              },
              {
                icon: BarChart3,
                label: 'Total Trades',
                value: actualBacktestResult.numberOfTrades.toString(),
                color: 'white'
              },
              {
                icon: Target,
                label: 'Win Rate',
                value: `${actualBacktestResult.winRate.toFixed(1)}%`,
                color: 'emerald'
              },
              {
                icon: Zap,
                label: 'Max Drawdown',
                value: `-${actualBacktestResult.maxDrawdown.toFixed(2)}%`,
                color: 'red'
              }
            ].map((metric, index) => (
              <div
                key={metric.label}
                className="bg-gradient-to-br from-[#0D0D0D] via-[#111111] to-[#0A0A0A] border border-white/[0.1] rounded-xl p-4 shadow-[0_8px_32px_rgba(0,0,0,0.4),inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-sm"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center gap-3 mb-2">
                  <div className="p-2 rounded-lg bg-gradient-to-br from-white/[0.1] to-white/[0.05] shadow-[inset_0_1px_0_rgba(255,255,255,0.2)] backdrop-blur-sm">
                    <metric.icon className="h-4 w-4 text-white/80" />
                  </div>
                  <span className="text-sm text-white/80 font-medium">{metric.label}</span>
                </div>
                <div className={`text-lg font-bold ${
                  metric.color === 'emerald' ? 'text-emerald-400' :
                  metric.color === 'red' ? 'text-red-400' : 'text-white'
                }`}>
                  {metric.value}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Chart Area - Fixed Height for Scrolling */}
      <div className={`relative z-10 transition-all duration-1000 delay-500 ${
        isAnimating ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
      }`}>

        {/* Chart Container - Fixed Height */}
        <div className="p-6 pt-4">
          <div className="h-[700px] bg-gradient-to-br from-[#0D0D0D] via-[#111111] to-[#0A0A0A] border border-white/[0.12] rounded-2xl overflow-hidden shadow-[0_20px_60px_rgba(0,0,0,0.5),inset_0_1px_0_rgba(255,255,255,0.1),inset_0_0_30px_rgba(34,197,94,0.08)] backdrop-blur-md">

            {/* Compact Chart Header */}
            <div className="flex items-center justify-between p-4 border-b border-white/[0.1] bg-gradient-to-r from-[#0A0A0A] via-[#0F0F0F] to-[#0A0A0A] backdrop-blur-sm">
              <div className="flex items-center gap-4">
                <div className="text-lg font-medium text-white tracking-wide" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                  {selectedSymbol}
                </div>
                {currentTradeIndex !== undefined && actualBacktestResult && (
                  <div className="text-white/60 text-sm">
                    {selectedSymbol} • {mainChartTimeframe} • {format(new Date(actualBacktestResult.trades[currentTradeIndex].date), 'MMM dd, yyyy')}
                  </div>
                )}
              </div>

              {/* Timeframe Controls */}
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 bg-[#0F0F11] rounded-lg p-1 border border-[#1A1A1C] shadow-inner">
                  {chartTimeframeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setMainChartTimeframe(option.value)}
                      disabled={isLoadingMainChart}
                      className={`px-2.5 py-1.5 text-xs font-medium rounded transition-all duration-300 ${
                        mainChartTimeframe === option.value
                          ? 'bg-white text-black shadow-sm'
                          : 'text-white/60 hover:text-white hover:bg-white/5'
                      } ${isLoadingMainChart ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      title={option.description}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Chart Content - Fixed Height */}
            <div className="relative bg-[#101010] h-[calc(700px-60px)]">
              {/* Trade Navigation - Top Left */}
              {currentTradeIndex !== undefined && actualBacktestResult && (
                <div className="absolute top-4 left-4 z-10 flex items-center bg-gradient-to-br from-black/60 to-black/40 border border-white/[0.08] rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-md overflow-hidden">
                  <button
                    onClick={() => {
                      const prevIndex = currentTradeIndex > 0 ? currentTradeIndex - 1 : actualBacktestResult.trades.length - 1;
                      handleTradeClick(prevIndex);
                    }}
                    className="p-1.5 hover:bg-green-500/10 transition-all duration-200 hover:shadow-[0_0_6px_rgba(34,197,94,0.2)]"
                  >
                    <svg className="h-3 w-3 text-white/70 hover:text-green-400 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>
                  <div className="px-2.5 py-1.5 text-white/90 text-xs font-medium border-x border-white/[0.06]">
                    {currentTradeIndex + 1}/{actualBacktestResult.trades.length}
                  </div>
                  <button
                    onClick={() => {
                      const nextIndex = currentTradeIndex < actualBacktestResult.trades.length - 1 ? currentTradeIndex + 1 : 0;
                      handleTradeClick(nextIndex);
                    }}
                    className="p-1.5 hover:bg-green-500/10 transition-all duration-200 hover:shadow-[0_0_6px_rgba(34,197,94,0.2)]"
                  >
                    <svg className="h-3 w-3 text-white/70 hover:text-green-400 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2.5}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              )}

              {currentTradeIndex !== undefined && mainChartData.length > 0 ? (
                <CleanCandlestickChart
                  symbol={selectedSymbol}
                  data={mainChartData}
                  tradeMarkers={[
                    {
                      timestamp: adjustTradeToMarketOpen(new Date(actualBacktestResult.trades[currentTradeIndex].date).getTime()),
                      price: actualBacktestResult.trades[currentTradeIndex].price,
                      type: actualBacktestResult.trades[currentTradeIndex].type === 'buy' ? 'entry' : 'exit',
                      label: `${actualBacktestResult.trades[currentTradeIndex].type.toUpperCase()} $${actualBacktestResult.trades[currentTradeIndex].price.toFixed(2)}`,
                      color: actualBacktestResult.trades[currentTradeIndex].type === 'buy' ? '#10b981' : '#ef4444',
                      tradeIndex: currentTradeIndex
                    }
                  ]}
                  height={640}
                  className="w-full h-full"
                  currentTradeIndex={currentTradeIndex}
                  allTrades={actualBacktestResult.trades}
                />
              ) : isLoadingMainChart ? (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="h-12 w-12 border-2 border-white/10 border-t-white/30 rounded-full animate-spin mb-4 mx-auto"></div>
                    <div className="text-white/70 text-lg mb-2">Loading {mainChartTimeframe} chart data...</div>
                    <div className="text-white/50 text-sm">Analyzing market conditions for selected trade</div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center max-w-4xl">
                    <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gradient-to-br from-white/[0.1] to-white/[0.05] flex items-center justify-center">
                      <svg className="h-8 w-8 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    </div>
                    <div className="text-white/70 text-xl mb-4 font-bold">Interactive Market Analysis</div>
                    <div className="text-white/50 text-base mb-8">Select any trade below to view detailed price action and market context</div>

                    {/* Horizontal Trade Selection - Bottom of Chart */}
                    <div className="w-full">
                      <div className="mb-4">
                        <h4 className="text-white/80 text-base font-semibold mb-2">Trade History ({actualBacktestResult.trades.length} trades)</h4>
                        <p className="text-white/60 text-sm">Click on any trade to analyze market conditions</p>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 max-h-60 overflow-y-auto scrollbar-hide">
                        {actualBacktestResult.trades.slice().reverse().map((trade, reversedIndex) => {
                          const originalIndex = actualBacktestResult.trades.length - 1 - reversedIndex;
                          return (
                            <div
                              key={reversedIndex}
                              className="bg-gradient-to-br from-[#0D0D0D] to-[#0A0A0A] border border-white/[0.08] rounded-xl p-3 hover:from-[#111111] hover:to-[#0D0D0D] hover:border-white/[0.15] transition-all duration-200 cursor-pointer group shadow-[inset_0_1px_0_rgba(255,255,255,0.05)]"
                              onClick={() => handleTradeClick(originalIndex)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className={`w-2.5 h-2.5 rounded-full ${
                                    trade.type === 'buy' ? 'bg-emerald-400 shadow-[0_0_8px_rgba(52,211,153,0.4)]' : 'bg-red-400 shadow-[0_0_8px_rgba(248,113,113,0.4)]'
                                  }`} />
                                  <span className={`text-sm font-medium ${
                                    trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                                  }`}>
                                    {trade.type.toUpperCase()}
                                  </span>
                                </div>
                                <span className="text-sm text-white font-medium">${trade.price.toFixed(2)}</span>
                              </div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom spacing for scrolling */}
      <div className="h-40"></div>

      {/* Add floating animation styles */}
      <style>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
          25% { transform: translateY(-10px) translateX(5px); opacity: 0.6; }
          50% { transform: translateY(-5px) translateX(-5px); opacity: 0.4; }
          75% { transform: translateY(-15px) translateX(3px); opacity: 0.7; }
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
      </div>
    </div>
  );
};

export default BacktestResults;
