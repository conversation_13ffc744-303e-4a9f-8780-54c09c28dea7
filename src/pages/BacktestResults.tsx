import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { X, TrendingUp, Target, BarChart3, Zap, ArrowLeft } from 'lucide-react';
import CleanCandlestickChart from '@/components/charts/CleanCandlestickChart';
import { fetchTradeChartData, adjustTradeToMarketOpen } from '@/services/cleanChartService';
import { useToast } from '@/components/ui/use-toast';

interface BacktestResult {
  symbol: string;
  startDate: string;
  endDate: string;
  totalReturn: number;
  buyAndHoldReturn: number;
  numberOfTrades: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: Array<{
    date: string;
    type: 'buy' | 'sell';
    price: number;
    signal: string;
    confidence: number;
  }>;
  performanceChart: Array<{
    date: string;
    agentValue: number;
    buyHoldValue: number;
  }>;
}

const BacktestResults: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const backtestResult = location.state?.backtestResult as BacktestResult;
  const agentName = location.state?.agentName as string;
  const selectedSymbol = location.state?.selectedSymbol as string;
  
  const [isAnimating, setIsAnimating] = useState(true);
  const [currentTradeIndex, setCurrentTradeIndex] = useState<number | undefined>(undefined);
  const [mainChartData, setMainChartData] = useState<any[]>([]);
  const [isLoadingMainChart, setIsLoadingMainChart] = useState(false);
  const [mainChartTimeframe, setMainChartTimeframe] = useState<'1min' | '5min' | '15min' | '30min' | '1hour' | '4hour' | 'daily'>('1hour');

  // Chart timeframe options
  const chartTimeframeOptions = [
    { value: '1min' as const, label: '1m', description: '1 minute' },
    { value: '5min' as const, label: '5m', description: '5 minutes' },
    { value: '15min' as const, label: '15m', description: '15 minutes' },
    { value: '30min' as const, label: '30m', description: '30 minutes' },
    { value: '1hour' as const, label: '1h', description: '1 hour' },
    { value: '4hour' as const, label: '4h', description: '4 hours' },
    { value: 'daily' as const, label: '1D', description: '1 day' }
  ];

  // Animation sequence
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsAnimating(false);
    }, 1500);
    return () => clearTimeout(timer);
  }, []);

  // Handle trade click
  const handleTradeClick = async (tradeIndex: number) => {
    if (!backtestResult?.trades || !backtestResult.trades[tradeIndex]) return;
    
    setCurrentTradeIndex(tradeIndex);
    setIsLoadingMainChart(true);
    
    try {
      const selectedTrade = backtestResult.trades[tradeIndex];
      const tradeTimestamp = new Date(selectedTrade.date).getTime();
      const adjustedTimestamp = adjustTradeToMarketOpen(tradeTimestamp);
      
      const chartData = await fetchTradeChartData(
        selectedSymbol,
        adjustedTimestamp,
        mainChartTimeframe
      );
      
      setMainChartData(chartData);
    } catch (error) {
      console.error('Error loading chart data:', error);
      toast({
        title: 'Chart Error',
        description: 'Failed to load chart data for this trade',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingMainChart(false);
    }
  };

  // Load chart data when timeframe changes
  useEffect(() => {
    if (currentTradeIndex !== undefined && backtestResult?.trades) {
      handleTradeClick(currentTradeIndex);
    }
  }, [mainChartTimeframe]);

  // Handle back navigation to main backtesting page
  const handleBack = () => {
    navigate('/agent-backtesting');
  };

  if (!backtestResult) {
    return (
      <div className="min-h-screen bg-[#0A0A0A] flex items-center justify-center">
        <div className="text-center">
          <div className="text-white/60 text-lg mb-4">No backtest results found</div>
          <button
            onClick={handleBack}
            className="px-6 py-3 bg-white text-black rounded-lg hover:bg-gray-200 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#0A0A0A] text-white overflow-hidden relative flex flex-col">
      {/* Subtle Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/[0.02] via-transparent to-white/[0.01]" />

      {/* Minimal Floating Particles */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-0.5 h-0.5 bg-white/20 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              animationDuration: `${4 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Compact Header with Performance Cards */}
      <div className={`relative z-50 flex-shrink-0 transition-all duration-1000 ${
        isAnimating ? 'translate-y-[-100%] opacity-0' : 'translate-y-0 opacity-100'
      }`}>

        {/* Top Navigation Bar */}
        <div className="flex items-center justify-between p-4 border-b border-white/[0.06] bg-[#0A0A0A]/95 backdrop-blur-sm">
          <div className="flex items-center gap-3">
            <button
              onClick={handleBack}
              className="p-2 rounded-lg bg-white/[0.05] hover:bg-white/[0.1] border border-white/[0.08] transition-all duration-300 group"
            >
              <ArrowLeft className="h-4 w-4 text-white/70 group-hover:text-white transition-colors" />
            </button>
            <div>
              <h1 className="text-lg font-bold text-white">AI Backtest Results</h1>
              <p className="text-white/60 text-xs">{agentName} • {selectedSymbol}</p>
            </div>
          </div>

          <button
            onClick={handleBack}
            className="p-2 rounded-lg bg-white/[0.05] hover:bg-white/[0.1] border border-white/[0.08] transition-all duration-300 group"
          >
            <X className="h-4 w-4 text-white/70 group-hover:text-white transition-colors" />
          </button>
        </div>

        {/* Performance Metrics Cards - Compact Top Section */}
        <div className={`p-4 bg-[#0A0A0A]/90 backdrop-blur-sm transition-all duration-800 delay-300 ${
          isAnimating ? 'translate-y-[-20px] opacity-0' : 'translate-y-0 opacity-100'
        }`}>
          <div className="grid grid-cols-4 gap-3">
            {[
              {
                icon: TrendingUp,
                label: 'Total Return',
                value: `${backtestResult.totalReturn > 0 ? '+' : ''}${backtestResult.totalReturn.toFixed(2)}%`,
                color: backtestResult.totalReturn > 0 ? 'emerald' : 'red'
              },
              {
                icon: BarChart3,
                label: 'Total Trades',
                value: backtestResult.numberOfTrades.toString(),
                color: 'white'
              },
              {
                icon: Target,
                label: 'Win Rate',
                value: `${backtestResult.winRate.toFixed(1)}%`,
                color: 'emerald'
              },
              {
                icon: Zap,
                label: 'Max Drawdown',
                value: `-${backtestResult.maxDrawdown.toFixed(2)}%`,
                color: 'red'
              }
            ].map((metric, index) => (
              <div
                key={metric.label}
                className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-3 shadow-[inset_0_1px_0_rgba(255,255,255,0.08)] hover:bg-white/[0.02] transition-all duration-300 group"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex items-center gap-2 mb-1">
                  <div className="p-1.5 rounded-md bg-white/[0.05] group-hover:bg-white/[0.08] transition-colors">
                    <metric.icon className="h-3.5 w-3.5 text-white/70" />
                  </div>
                  <span className="text-xs text-white/70 font-medium">{metric.label}</span>
                </div>
                <div className={`text-lg font-bold ${
                  metric.color === 'emerald' ? 'text-emerald-400' :
                  metric.color === 'red' ? 'text-red-400' : 'text-white'
                } group-hover:scale-105 transition-transform`}>
                  {metric.value}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Main Chart Area - Takes Up Remaining Screen Space */}
      <div className={`relative z-10 flex-1 flex flex-col transition-all duration-1000 delay-500 ${
        isAnimating ? 'scale-95 opacity-0' : 'scale-100 opacity-100'
      }`}>

        {/* Chart Container - Maximum Space */}
        <div className="flex-1 p-4 pt-2">
          <div className="h-full bg-[#0D0D0D] border border-white/[0.06] rounded-lg overflow-hidden shadow-[inset_0_1px_0_rgba(255,255,255,0.08)]">

            {/* Compact Chart Header */}
            <div className="flex items-center justify-between p-3 border-b border-white/[0.06] bg-gradient-to-r from-[#0D0D0D] to-[#111111]">
              <div className="flex items-center gap-3">
                <div className="text-lg font-bold text-white flex items-center gap-2">
                  📊 Market Analysis
                  {currentTradeIndex !== undefined && (
                    <span className="text-xs bg-white/[0.1] text-white/80 px-2 py-1 rounded-full">
                      Trade {currentTradeIndex + 1}/{backtestResult.trades.length}
                    </span>
                  )}
                </div>
                {currentTradeIndex !== undefined && (
                  <div className="text-white/60 text-sm">
                    {selectedSymbol} • {mainChartTimeframe} intervals
                  </div>
                )}
              </div>

              {/* Timeframe Controls */}
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1 bg-[#0F0F11] rounded-lg p-1 border border-[#1A1A1C] shadow-inner">
                  {chartTimeframeOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => setMainChartTimeframe(option.value)}
                      disabled={isLoadingMainChart}
                      className={`px-2.5 py-1.5 text-xs font-medium rounded transition-all duration-300 ${
                        mainChartTimeframe === option.value
                          ? 'bg-white text-black shadow-sm'
                          : 'text-white/60 hover:text-white hover:bg-white/5'
                      } ${isLoadingMainChart ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                      title={option.description}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Chart Content - Maximum Height */}
            <div className="relative bg-[#0A0A0A] flex-1">
              {currentTradeIndex !== undefined && mainChartData.length > 0 ? (
                <CleanCandlestickChart
                  symbol={selectedSymbol}
                  data={mainChartData}
                  tradeMarkers={[
                    {
                      timestamp: adjustTradeToMarketOpen(new Date(backtestResult.trades[currentTradeIndex].date).getTime()),
                      price: backtestResult.trades[currentTradeIndex].price,
                      type: backtestResult.trades[currentTradeIndex].type === 'buy' ? 'entry' : 'exit',
                      label: `${backtestResult.trades[currentTradeIndex].type.toUpperCase()} $${backtestResult.trades[currentTradeIndex].price.toFixed(2)}`,
                      color: backtestResult.trades[currentTradeIndex].type === 'buy' ? '#10b981' : '#ef4444',
                      tradeIndex: currentTradeIndex
                    }
                  ]}
                  height={800}
                  className="w-full h-full"
                  currentTradeIndex={currentTradeIndex}
                  allTrades={backtestResult.trades}
                />
              ) : isLoadingMainChart ? (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center">
                    <div className="h-12 w-12 border-2 border-white/10 border-t-white/30 rounded-full animate-spin mb-4 mx-auto"></div>
                    <div className="text-white/70 text-lg mb-2">Loading {mainChartTimeframe} chart data...</div>
                    <div className="text-white/50 text-sm">Analyzing market conditions for selected trade</div>
                  </div>
                </div>
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center max-w-4xl">
                    <div className="text-5xl mb-6 animate-float">📈</div>
                    <div className="text-white/70 text-xl mb-4 font-bold">Interactive Market Analysis</div>
                    <div className="text-white/50 text-base mb-8">Select any trade below to view detailed price action and market context</div>

                    {/* Horizontal Trade Selection - Bottom of Chart */}
                    <div className="w-full">
                      <div className="mb-4">
                        <h4 className="text-white/80 text-base font-semibold mb-2">Trade History ({backtestResult.trades.length} trades)</h4>
                        <p className="text-white/60 text-sm">Click on any trade to analyze market conditions</p>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-2 max-h-60 overflow-y-auto scrollbar-hide">
                        {backtestResult.trades.slice().reverse().map((trade, reversedIndex) => {
                          const originalIndex = backtestResult.trades.length - 1 - reversedIndex;
                          return (
                            <div
                              key={reversedIndex}
                              className="bg-[#0D0D0D] border border-white/[0.06] rounded-lg p-2.5 hover:bg-white/[0.02] hover:border-white/[0.12] transition-all duration-200 cursor-pointer group"
                              onClick={() => handleTradeClick(originalIndex)}
                            >
                              <div className="flex items-center justify-between mb-1.5">
                                <div className="flex items-center gap-1.5">
                                  <div className={`w-2 h-2 rounded-full ${
                                    trade.type === 'buy' ? 'bg-emerald-400' : 'bg-red-400'
                                  }`} />
                                  <span className={`text-xs font-semibold ${
                                    trade.type === 'buy' ? 'text-emerald-400' : 'text-red-400'
                                  }`}>
                                    {trade.type.toUpperCase()}
                                  </span>
                                </div>
                                <span className="text-xs text-white font-mono">${trade.price.toFixed(2)}</span>
                              </div>
                              <div className="flex items-center justify-between text-xs text-white/60 mb-1">
                                <span className="font-mono text-xs">{trade.date.split(' ')[0]}</span>
                                <span className="font-medium">{trade.confidence}%</span>
                              </div>
                              <div className="text-xs text-white/50 truncate">{trade.signal}</div>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Add floating animation styles */}
      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) translateX(0px); opacity: 0.3; }
          25% { transform: translateY(-10px) translateX(5px); opacity: 0.6; }
          50% { transform: translateY(-5px) translateX(-5px); opacity: 0.4; }
          75% { transform: translateY(-15px) translateX(3px); opacity: 0.7; }
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default BacktestResults;
