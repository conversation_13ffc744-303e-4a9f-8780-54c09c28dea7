import React, { useState, useEffect } from 'react';
import { Search, Filter, Star, Download, Calendar, TrendingUp, Grid, List } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { useResponsive } from '@/hooks/useResponsive';
import { getFreeMarketplaceAgents, discoverAgents, importAgent, type PublishedAgent, type AgentCategory, type DiscoverFilters } from '@/services/discoverService';
import { getPaidMarketplaceAgents, type MarketplaceAgent, type MarketplaceFilters } from '@/services/marketplaceService';
import AgentCard from '@/components/discover/AgentCard';
import AgentFilters from '@/components/discover/AgentFilters';
import MobileFiltersDropdown from '@/components/discover/MobileFiltersDropdown';
import PublishAgentModal from '@/components/discover/PublishAgentModal';
import MarketplaceAgentCard from '@/components/marketplace/MarketplaceAgentCard';
import { useNavigate } from 'react-router-dom';

const Discover: React.FC = () => {
  const { toast } = useToast();
  const { isMobile, classes } = useResponsive();
  const navigate = useNavigate();
  const [agents, setAgents] = useState<PublishedAgent[]>([]);
  const [marketplaceAgents, setMarketplaceAgents] = useState<MarketplaceAgent[]>([]);
  const [categories, setCategories] = useState<AgentCategory[]>([]);
  const [activeTab, setActiveTab] = useState<'free' | 'paid'>('paid');
  const [loading, setLoading] = useState(true);
  const [importing, setImporting] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(isMobile ? 'grid' : 'list');
  const [showPublishModal, setShowPublishModal] = useState(false);
  
  // Filter states
  const [filters, setFilters] = useState<DiscoverFilters>({
    search: '',
    category: '',
    sortBy: 'newest',
    sortOrder: 'desc',
    limit: 20,
    offset: 0
  });

  // Pagination state
  const [pagination, setPagination] = useState({
    total: 0,
    hasMore: false
  });

  // Load agents
  const loadAgents = async (newFilters?: DiscoverFilters, append = false) => {
    try {
      setLoading(!append);

      const filtersToUse = newFilters || filters;

      if (activeTab === 'paid') {
        // STRICT ISOLATION: Load ONLY paid agents (never free agents)
        console.log('💰 Loading PAID marketplace agents with strict isolation');
        const marketplaceFilters: MarketplaceFilters = {
          search: filtersToUse.search,
          tags: filtersToUse.tags,
          sort_by: filtersToUse.sortBy === 'newest' ? 'newest' :
                   filtersToUse.sortBy === 'popular' ? 'popular' :
                   filtersToUse.sortBy === 'downloads' ? 'sales' : 'newest',
          limit: filtersToUse.limit,
          offset: filtersToUse.offset
        };

        const response = await getPaidMarketplaceAgents(marketplaceFilters);

        if (response.success) {
          if (append) {
            setMarketplaceAgents(prev => [...prev, ...response.agents]);
          } else {
            setMarketplaceAgents(response.agents);
          }
          setPagination({
            total: response.total,
            hasMore: response.agents.length === (filtersToUse.limit || 20)
          });
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description: response.error || "Failed to load marketplace agents"
          });
        }
      } else {
        // STRICT ISOLATION: Load ONLY free agents (never paid agents)
        console.log('🆓 Loading FREE marketplace agents with strict isolation');
        const response = await getFreeMarketplaceAgents(filtersToUse);

        if (response.success) {
          if (append) {
            setAgents(prev => [...prev, ...response.agents]);
          } else {
            setAgents(response.agents);
          }
          setCategories(response.categories);
          setPagination({
            total: response.pagination.total,
            hasMore: response.pagination.hasMore
          });
        } else {
          toast({
            variant: "destructive",
            title: "Error",
            description: response.error || "Failed to load agents"
          });
        }
      }
    } catch (error) {
      console.error('Error loading agents:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setLoading(false);
    }
  };

  // Load categories separately to ensure they're always available
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await discoverAgents({ limit: 1 }); // Just get categories
        if (response.success) {
          setCategories(response.categories);
        }
      } catch (error) {
        console.error('Error loading categories:', error);
      }
    };

    loadCategories();
  }, []); // Load categories once on mount

  // Initial load and when tab changes
  useEffect(() => {
    loadAgents();

    // Check for purchase success/failure in URL (only on first load)
    if (activeTab === 'paid') {
      const urlParams = new URLSearchParams(window.location.search);
      const purchaseStatus = urlParams.get('purchase');

      if (purchaseStatus === 'success') {
        // Get session ID from URL to verify purchase
        const sessionId = urlParams.get('session_id');

        if (sessionId) {
          // Call a verification endpoint to ensure purchase was processed
          const verifyPurchase = async () => {
            const session = await supabase.auth.getSession();
            const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/verify-purchase`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${session.data.session?.access_token}`
              },
              body: JSON.stringify({ session_id: sessionId })
            });
            return response;
          };

          verifyPurchase().then(async (response) => {
            const data = await response.json();

            if (data.success && data.agent_created) {
              toast({
                title: "🎉 Purchase Successful!",
                description: `"${data.agent_name}" has been added to your library!`,
                duration: 5000
              });
            } else {
              toast({
                title: "⚠️ Purchase Processing",
                description: "Your payment was successful but the agent is still being processed. Check back in a moment.",
                duration: 5000
              });
            }
          }).catch(() => {
            toast({
              title: "🎉 Purchase Successful!",
              description: "Your agent should appear in your library shortly.",
              duration: 5000
            });
          });
        } else {
          toast({
            title: "🎉 Purchase Successful!",
            description: "Your agent has been added to your library. Check Agent Management to see it!",
            duration: 5000
          });
        }

        // Clean up URL
        window.history.replaceState({}, '', '/marketplace');
      } else if (purchaseStatus === 'cancelled') {
        toast({
          title: "Purchase Cancelled",
          description: "Your purchase was cancelled. No charges were made.",
          variant: "destructive",
          duration: 3000
        });
        // Clean up URL
        window.history.replaceState({}, '', '/marketplace');
      }
    }
  }, [activeTab]);

  // Handle tab change
  const handleTabChange = (tab: string) => {
    const newTab = tab as 'free' | 'paid';
    setActiveTab(newTab);
    setAgents([]);
    setMarketplaceAgents([]);
    setPagination({ total: 0, hasMore: false });
  };

  // Handle filter changes
  const handleFilterChange = (newFilters: Partial<DiscoverFilters>) => {
    const updatedFilters = {
      ...filters,
      ...newFilters,
      offset: 0 // Reset pagination when filters change
    };
    setFilters(updatedFilters);
    loadAgents(updatedFilters);
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    handleFilterChange({ search: searchTerm });
  };

  // Handle load more
  const handleLoadMore = () => {
    const newFilters = {
      ...filters,
      offset: agents.length
    };
    setFilters(newFilters);
    loadAgents(newFilters, true);
  };

  // Handle import agent
  const handleImportAgent = async (publishedAgentId: string, customName?: string) => {
    try {
      setImporting(publishedAgentId);
      
      const response = await importAgent({
        publishedAgentId,
        customName
      });
      
      if (response.success) {
        toast({
          title: "Success",
          description: response.message || "Agent imported successfully"
        });
        
        // Update download count locally
        setAgents(prev => prev.map(agent => 
          agent.id === publishedAgentId 
            ? { ...agent, downloadCount: agent.downloadCount + 1 }
            : agent
        ));
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: response.error || "Failed to import agent"
        });
      }
    } catch (error) {
      console.error('Error importing agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setImporting(null);
    }
  };

  // Handle purchase agent
  const handlePurchaseAgent = async (agentId: string) => {
    try {
      setImporting(agentId);

      // For now, show a demo message since we don't have full Stripe integration
      toast({
        title: "Purchase Demo",
        description: "This would initiate the purchase flow. Full Stripe integration coming soon!",
        duration: 3000
      });

      // Simulate purchase delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast({
        title: "Success",
        description: "Agent would be added to your library after successful payment!"
      });
    } catch (error) {
      console.error('Error purchasing agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "An unexpected error occurred"
      });
    } finally {
      setImporting(null);
    }
  };

  const handleBacktestAgent = (agent: MarketplaceAgent) => {
    // Navigate to backtest page with marketplace agent data
    navigate('/agent-backtesting', {
      state: {
        marketplaceAgent: {
          id: agent.id,
          name: agent.name,
          description: agent.description,
          configuration: agent.configuration,
          isMarketplaceAgent: true
        }
      }
    });
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className={`max-w-7xl mx-auto ${isMobile ? 'px-3 py-4' : 'px-8 py-8'}`}>
        {/* Enhanced Header */}
        <div className={`${isMobile ? 'mb-8' : 'mb-12'}`}>
          <div className={`flex ${isMobile ? 'flex-col gap-6' : 'items-start justify-between'} ${isMobile ? 'mb-6' : 'mb-8'}`}>
            <div className="flex-1">
              <h1 className={`${isMobile ? 'text-2xl' : 'text-3xl'} font-medium text-white mb-3`} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>
                Agent Marketplace
              </h1>
              <p className="text-white/60 text-base mb-6" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                Discover, purchase, and share AI trading agents built by the community
              </p>

              {/* Unified Search Bar */}
              <div className="relative max-w-lg">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white" />
                <Input
                  placeholder="Search agents, strategies, indicators..."
                  value={filters.search || ''}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-12 pr-4 bg-gradient-to-br from-white/[0.06] to-white/[0.03] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.08] focus:outline-none focus:ring-0 h-12 text-sm rounded-xl shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
                  style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                />
              </div>
            </div>

            <div className={`flex items-center ${isMobile ? 'gap-3 w-full' : 'gap-4'}`}>
              <Button
                onClick={() => setShowPublishModal(true)}
                className={`bg-gradient-to-br from-white to-gray-100 hover:from-gray-100 hover:to-white text-black border-0 shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_4px_12px_rgba(255,255,255,0.2)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.4),0_6px_16px_rgba(255,255,255,0.3)] transition-all duration-300 font-medium ${isMobile ? 'flex-1 px-3 py-2 text-xs h-10' : 'px-4 py-2 text-xs h-10'} rounded-xl`}
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                Publish Agent
              </Button>
              {!isMobile && (
                <div className="flex items-center gap-1 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.08] rounded-xl p-1 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className={`h-8 px-2.5 rounded-lg transition-all duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-white/[0.12] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]'
                        : 'text-white/60 hover:text-white hover:bg-white/[0.06]'
                    }`}
                  >
                    <Grid className="w-3.5 h-3.5" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className={`h-8 px-2.5 rounded-lg transition-all duration-200 ${
                      viewMode === 'list'
                        ? 'bg-white/[0.12] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]'
                        : 'text-white/60 hover:text-white hover:bg-white/[0.06]'
                    }`}
                  >
                    <List className="w-3.5 h-3.5" />
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Filter Bar */}
          <div className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}>
            {/* Mobile Filters */}
            {isMobile && (
              <div className="w-full">
                <MobileFiltersDropdown
                  categories={categories}
                  filters={filters}
                  onFilterChange={handleFilterChange}
                />
              </div>
            )}

            {/* Grouped Controls */}
            <div className={`flex items-center gap-3 ${isMobile ? 'w-full' : ''}`}>
              {/* Price Type Filter */}
              <div className="flex items-center gap-1 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.08] rounded-xl p-1 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveTab('paid')}
                  className={`h-8 px-3 rounded-lg transition-all duration-200 text-xs font-medium ${
                    activeTab === 'paid'
                      ? 'bg-white/[0.12] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]'
                      : 'text-white/60 hover:text-white hover:bg-white/[0.06]'
                  }`}
                >
                  Premium
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setActiveTab('free')}
                  className={`h-8 px-3 rounded-lg transition-all duration-200 text-xs font-medium ${
                    activeTab === 'free'
                      ? 'bg-white/[0.12] text-white shadow-[inset_0_1px_0_rgba(255,255,255,0.2)]'
                      : 'text-white/60 hover:text-white hover:bg-white/[0.06]'
                  }`}
                >
                  Free
                </Button>
              </div>

              {/* Sort Dropdown */}
              <Select value={filters.sortBy} onValueChange={(value) => handleFilterChange({ sortBy: value as any })}>
                <SelectTrigger className={`${isMobile ? 'flex-1' : 'w-40'} h-8 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.08] text-white focus:outline-none focus:ring-0 transition-all duration-200 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] text-xs`} style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-gradient-to-br from-[#1A1A1A] to-[#151515] border border-white/[0.08] text-white shadow-[0_20px_60px_rgba(0,0,0,0.6)] rounded-lg backdrop-blur-md">
                  <SelectItem value="newest" className="text-white hover:bg-white/[0.06] focus:bg-white/[0.06] rounded-lg text-xs">Newest</SelectItem>
                  <SelectItem value="popular" className="text-white hover:bg-white/[0.06] focus:bg-white/[0.06] rounded-lg text-xs">Most Popular</SelectItem>
                  <SelectItem value="rating" className="text-white hover:bg-white/[0.06] focus:bg-white/[0.06] rounded-lg text-xs">Highest Rated</SelectItem>
                  <SelectItem value="downloads" className="text-white hover:bg-white/[0.06] focus:bg-white/[0.06] rounded-lg text-xs">Most Downloaded</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className={`grid grid-cols-1 ${isMobile ? 'gap-4' : 'lg:grid-cols-4 gap-8'}`}>
          {/* Filters Sidebar - Desktop Only */}
          {!isMobile && (
            <div className="lg:col-span-1">
              <AgentFilters
                categories={categories}
                filters={filters}
                onFilterChange={handleFilterChange}
              />
            </div>
          )}

          {/* Agents Grid/List */}
          <div className={`${isMobile ? 'col-span-1' : 'lg:col-span-3'}`}>
            {activeTab === 'paid' ? (
              // Paid agents (marketplace)
              loading && marketplaceAgents.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/20 mx-auto mb-4"></div>
                    <p className="text-white/50" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Loading marketplace agents...</p>
                  </div>
                </div>
              ) : marketplaceAgents.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-white/40 mb-4">
                    <Search className="w-16 h-16 mx-auto mb-6 opacity-30" />
                    <h3 className="text-xl font-medium mb-2 text-white/60" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>No paid agents found</h3>
                    <p className="text-white/40" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Try adjusting your search criteria or check back later</p>
                  </div>
                </div>
              ) : (
                <>
                  <div className={isMobile || viewMode === 'grid'
                    ? `grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'}`
                    : 'space-y-4'
                  }>
                    {marketplaceAgents.map((agent) => (
                      <MarketplaceAgentCard
                        key={agent.id}
                        agent={agent}
                        viewMode={isMobile ? 'grid' : viewMode}
                        onPurchase={handlePurchaseAgent}
                        purchasing={importing === agent.id}
                        onPricingUpdate={() => loadMarketplaceAgents()}
                        onBacktest={handleBacktestAgent}
                      />
                    ))}
                  </div>

                  {/* Enhanced Load More Button */}
                  {pagination.hasMore && (
                    <div className="text-center mt-8">
                      <Button
                        onClick={handleLoadMore}
                        disabled={loading}
                        variant="outline"
                        className="border-white/[0.08] text-white/70 hover:text-white hover:bg-white/[0.04] hover:border-white/[0.15] bg-white/[0.02] transition-all duration-300 px-6 py-3 h-11 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.15),0_2px_8px_rgba(0,0,0,0.1)] font-medium"
                        style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                      >
                        {loading ? 'Loading...' : 'Load More'}
                      </Button>
                    </div>
                  )}
                </>
              )
            ) : (
              // Free agents (existing discover functionality)
              loading && agents.length === 0 ? (
                <div className="flex items-center justify-center h-64">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white/20 mx-auto mb-4"></div>
                    <p className="text-white/50" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Loading free agents...</p>
                  </div>
                </div>
              ) : agents.length === 0 ? (
                <div className="text-center py-16">
                  <div className="text-white/40 mb-4">
                    <Search className="w-16 h-16 mx-auto mb-6 opacity-30" />
                    <h3 className="text-xl font-medium mb-2 text-white/60" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>No free agents found</h3>
                    <p className="text-white/40" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>Try adjusting your search criteria or filters</p>
                  </div>
                </div>
              ) : (
                <>
                  <div className={isMobile || viewMode === 'grid'
                    ? `grid ${isMobile ? 'grid-cols-2 gap-3' : 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'}`
                    : 'space-y-4'
                  }>
                    {agents.map((agent) => (
                      <AgentCard
                        key={agent.id}
                        agent={agent}
                        viewMode={isMobile ? 'grid' : viewMode}
                        onImport={handleImportAgent}
                        importing={importing === agent.id}
                      />
                    ))}
                  </div>

                  {/* Enhanced Load More Button */}
                  {pagination.hasMore && (
                    <div className="text-center mt-8">
                      <Button
                        onClick={handleLoadMore}
                        disabled={loading}
                        variant="outline"
                        className="border-white/[0.08] text-white/70 hover:text-white hover:bg-white/[0.04] hover:border-white/[0.15] bg-white/[0.02] transition-all duration-300 px-6 py-3 h-11 rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.15),0_2px_8px_rgba(0,0,0,0.1)] font-medium"
                        style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
                      >
                        {loading ? 'Loading...' : 'Load More'}
                      </Button>
                    </div>
                  )}
                </>
              )
            )}
          </div>
        </div>
      </div>

      {/* Publish Agent Modal */}
      <PublishAgentModal
        isOpen={showPublishModal}
        onClose={() => setShowPublishModal(false)}
        categories={categories}
        onSuccess={() => {
          setShowPublishModal(false);
          loadAgents(); // Refresh the list
        }}
      />
    </div>
  );
};

export default Discover;
