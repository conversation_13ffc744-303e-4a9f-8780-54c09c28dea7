import { useState, useEffect } from 'react';
import { Check, Bot, TrendingUp } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useSubscriptionType } from '@/hooks/useSubscriptionType';
import { loadStripe } from '@stripe/stripe-js';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

// Stripe configurations based on user creation date
const STRIPE_CONFIGS = {
  // For users created before 6/3/25 (legacy/old)
  legacy: {
    publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
  },
  // For users created on or after 6/3/25 (new)
  current: {
    publicKey: 'pk_live_51RRMm0FagIdq3bE6dUumMlEEji4aFqLdzKxEMjChlGeATAcIhFmT6QhVEqYWV85kGGPxGRGhuvXIi7GZx52Oa1W300OyRpM1LQ',
  }
};

// Utility function to determine if user was created before the cutoff date (6/3/25)
const isLegacyUser = (userCreatedAt: string): boolean => {
  const cutoffDate = new Date('2025-06-03T00:00:00Z');
  const userDate = new Date(userCreatedAt);
  return userDate < cutoffDate;
};

// Function to get appropriate Stripe configuration for a user
const getStripeConfig = (userCreatedAt: string) => {
  return isLegacyUser(userCreatedAt) ? STRIPE_CONFIGS.legacy : STRIPE_CONFIGS.current;
};

const Subscription = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { subscriptionType, isLoading, canUpgrade } = useSubscriptionType();
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [userCreatedAt, setUserCreatedAt] = useState<string | null>(null);

  // Get current user and their creation date
  useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);

      if (user) {
        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single();

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let createdAt = userProfile?.created_at;
        if (!createdAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          createdAt = authUser?.user?.created_at || new Date().toISOString();
        }
        setUserCreatedAt(createdAt);
      }
    };
    getCurrentUser();
  }, []);

  // Plan configurations with correct price IDs based on user creation date
  const getPlans = () => {
    if (!userCreatedAt) return [];

    const isLegacy = isLegacyUser(userCreatedAt);

    return [
      {
        id: 'basic',
        name: 'Basic',
        price: '$5.99',
        period: '/week',
        priceId: isLegacy ? 'price_1RVyDZFagIdq3bE61UyNGiAj' : 'price_1ROYLKDebmd1GpTvct491Kw6',
        description: 'Essential features to get started',
        icon: Bot,
        features: [
          'All 7 AI Agents',
          'Stock and Crypto Analysis',
          'Instant Trading Information',
          '100 messages per day',
          '5 year Portfolio Backtesting',
          'Create 1 Portfolio'
        ],
        limitations: [
          'No Agent Builder access',
          'No Discover page access',
          'No Stock Search access',
          'No Stock Scanner access'
        ]
      },
      {
        id: 'pro',
        name: 'Pro',
        price: '$9.99',
        period: '/week',
        priceId: isLegacy ? 'price_1RVyE6FagIdq3bE6RP8yRJEv' : 'price_1ROYKjDebmd1GpTv5oYNMKMv',
        description: 'Full access to all premium features',
        icon: TrendingUp,
        popular: true,
        spotlight: true,
        features: [
          'Everything in Basic',
          'Access to Agent Builder',
          'Discover Community Agents',
          'Advanced Stock Search',
          'Stock Scanner Tools',
          '200 messages per day',
          'Create 3 Portfolios',
          'Priority Support'
        ],
        benefits: [
          'Build custom trading agents',
          'Access community strategies',
          'Advanced market analysis',
          'Professional trading tools'
        ]
      }
    ];
  };

  const plans = getPlans();

  // Handle plan selection and Stripe checkout using correct auth method
  const handleSelectPlan = async (plan: any) => {
    if (isProcessing || !currentUser || !userCreatedAt) return;

    setIsProcessing(true);

    try {
      // Get the session token for authorization
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        throw new Error("No authentication token found");
      }

      // Determine if this is an upgrade/downgrade or new subscription
      const isExistingSubscriber = subscriptionType && subscriptionType !== 'basic';
      const action = isExistingSubscriber ? 'update-subscription' : 'create-checkout-session';

      console.log('🔄 Subscription action:', {
        currentSubscriptionType: subscriptionType,
        targetPlan: plan.id,
        isExistingSubscriber,
        action
      });

      // Create checkout session using the correct endpoint
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          action,
          priceId: plan.priceId,
          returnUrl: window.location.origin + '/subscription/manage?success=true'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Server returned ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      const { sessionId } = data;

      if (!sessionId) {
        throw new Error('No session ID returned from server');
      }

      // Get the correct Stripe publishable key based on user creation date
      const stripeConfig = getStripeConfig(userCreatedAt);

      // Redirect to Stripe Checkout using the correct publishable key
      const stripe = await loadStripe(stripeConfig.publicKey);
      if (!stripe) throw new Error("Stripe failed to load");

      const { error } = await stripe.redirectToCheckout({ sessionId });
      if (error) throw new Error(`Stripe redirect failed: ${error.message}`);

    } catch (error) {
      console.error('Error creating checkout session:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to start checkout process. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Determine current plan
  const getCurrentPlan = () => {
    if (isLoading) return null;
    return plans.find(p => p.id === subscriptionType) || plans[0];
  };

  const currentPlan = getCurrentPlan();

  // If user is not on basic plan, show a different message
  if (!isLoading && !canUpgrade()) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center px-8">
            <div className="w-16 h-16 mx-auto mb-6 bg-green-500/20 rounded-full flex items-center justify-center">
              <Check className="w-8 h-8 text-green-400" />
            </div>
            <h1 className="text-3xl font-normal text-white mb-4 font-sans">
              You're All Set!
            </h1>
            <p className="text-white/60 text-base font-sans leading-relaxed mb-8">
              You're currently on the <span className="text-white font-medium">{currentPlan?.name || 'Pro'}</span> plan with full access to all features.
            </p>
            <div className="flex gap-4 justify-center">
              <button
                onClick={() => navigate('/subscription/manage')}
                className="px-6 py-3 bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08] rounded-lg transition-colors duration-200 font-sans"
              >
                View Plans
              </button>
              <button
                onClick={() => {
                  if (!userCreatedAt) return;
                  const isLegacy = isLegacyUser(userCreatedAt);
                  const billingPortalUrl = isLegacy
                    ? 'https://billing.stripe.com/p/login/fZeaGq9Iw2hAg929AA'
                    : 'https://billing.stripe.com/p/login/9B6bJ1go99YV5Vw06mds400';
                  window.open(billingPortalUrl, '_blank');
                }}
                disabled={!userCreatedAt}
                className="px-6 py-3 bg-white hover:bg-white/95 text-black rounded-lg transition-colors duration-200 font-sans disabled:opacity-50"
              >
                {!userCreatedAt ? 'Loading...' : 'Manage Billing'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state if user creation date is not yet loaded
  if (!userCreatedAt) {
    return (
      <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
        <div className="flex-1 flex items-center justify-center">
          <div className="max-w-2xl mx-auto text-center px-8">
            <div className="w-16 h-16 mx-auto mb-6 bg-white/[0.02] rounded-full flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-white/20 border-t-white/60 rounded-full animate-spin"></div>
            </div>
            <h1 className="text-3xl font-normal text-white mb-4 font-sans">
              Loading Plans...
            </h1>
            <p className="text-white/60 text-base font-sans leading-relaxed">
              Please wait while we prepare your subscription options
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full bg-[#0A0A0A] text-white flex flex-col overflow-y-auto">
      {/* Clean Header */}
      <div className="px-8 py-12">
        <div className="max-w-5xl mx-auto text-center">
          <h1 className="text-3xl font-normal text-white mb-4 font-sans">
            Choose Your Plan
          </h1>
          <p className="text-white/60 text-base font-sans leading-relaxed">
            Unlock advanced AI trading capabilities with our weekly plans
          </p>
        </div>
      </div>

      {/* Plans Section */}
      <div className="flex-1 px-8">
        <div className="max-w-5xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {plans.map((plan) => {
              const Icon = plan.icon;
              const isSpotlight = plan.spotlight;
              const isCurrent = currentPlan?.id === plan.id;

              return (
                <div
                  key={plan.id}
                  className={`relative rounded-xl border transition-all duration-200 ${
                    isSpotlight
                      ? 'bg-white/[0.02] border-white/[0.12] scale-105'
                      : isCurrent
                      ? 'bg-white/[0.04] border-white/[0.12]'
                      : 'bg-white/[0.01] border-white/[0.06] hover:border-white/[0.10]'
                  }`}
                >
                  {/* Popular Badge */}
                  {isSpotlight && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-white text-black rounded-full text-xs font-medium">
                        Most Popular
                      </div>
                    </div>
                  )}

                  {/* Current Plan Badge */}
                  {isCurrent && (
                    <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                      <div className="px-3 py-1 bg-green-500 text-white rounded-full text-xs font-medium">
                        Current Plan
                      </div>
                    </div>
                  )}

                  <div className="p-6">
                    {/* Plan Header */}
                    <div className="text-center mb-6">
                      <div className="flex items-center justify-center gap-2 mb-2">
                        <Icon className="w-5 h-5 text-white/80" />
                        <h3 className="text-xl font-medium text-white font-sans">{plan.name}</h3>
                      </div>
                      <p className="text-white/50 text-sm font-sans mb-4">{plan.description}</p>

                      <div className="flex items-baseline justify-center gap-1">
                        <span className="text-3xl font-normal text-white font-sans">
                          {plan.price}
                        </span>
                        <span className="text-white/50 text-sm font-sans">{plan.period}</span>
                      </div>
                    </div>

                    {/* Features List */}
                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, index) => (
                        <div key={index} className="flex items-center gap-3">
                          <Check className="w-4 h-4 text-green-400 flex-shrink-0" />
                          <span className="text-white/70 text-sm font-sans">{feature}</span>
                        </div>
                      ))}
                    </div>

                    {/* Limitations for Basic Plan */}
                    {plan.limitations && (
                      <div className="space-y-2 mb-6 p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]">
                        <p className="text-white/60 text-xs font-sans font-medium mb-2">Limitations:</p>
                        {plan.limitations.map((limitation, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <div className="w-1 h-1 bg-white/30 rounded-full flex-shrink-0"></div>
                            <span className="text-white/50 text-xs font-sans">{limitation}</span>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Action Button */}
                    <button
                      onClick={() => handleSelectPlan(plan)}
                      disabled={isCurrent || isProcessing || !userCreatedAt}
                      className={`w-full py-2.5 text-sm font-medium rounded-lg transition-all duration-200 font-sans ${
                        isCurrent
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : !userCreatedAt
                          ? 'bg-white/[0.03] text-white/40 cursor-not-allowed border border-white/[0.08]'
                          : isSpotlight
                          ? 'bg-white hover:bg-white/95 text-black shadow-[inset_0_1px_2px_rgba(0,0,0,0.03),0_1px_3px_rgba(0,0,0,0.08)]'
                          : 'bg-white/[0.03] hover:bg-white/[0.06] text-white/80 border border-white/[0.08]'
                      }`}
                    >
                      {isProcessing ? (
                        <div className="flex items-center justify-center gap-2">
                          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                          Processing...
                        </div>
                      ) : !userCreatedAt ? (
                        'Loading...'
                      ) : isCurrent ? (
                        'Current Plan'
                      ) : (
                        `Upgrade to ${plan.name}`
                      )}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Simple Footer */}
          <div className="mt-12 text-center">
            <p className="text-white/50 text-sm font-sans">
              Cancel anytime • Secure payments by Stripe
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
