import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { UniversalButton } from '@/components/ui/universal-button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Search, Loader2, Sparkles, Settings, MoreVertical, ArrowLeft } from 'lucide-react';
import { getAgents, deleteAgent, Agent, saveAgent } from '@/services/agentService';
import AgentRunDialog from '@/components/agent-builder/AgentRunDialog';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';

const AgentManagement: React.FC = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [filteredAgents, setFilteredAgents] = useState<Agent[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedAgentId, setSelectedAgentId] = useState<string | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [isRunDialogOpen, setIsRunDialogOpen] = useState<boolean>(false);
  const [agentToDelete, setAgentToDelete] = useState<Agent | null>(null);
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [newAgentName, setNewAgentName] = useState<string>('');
  const [newAgentDescription, setNewAgentDescription] = useState<string>('');
  const [isCreating, setIsCreating] = useState<boolean>(false);

  // Load agents
  useEffect(() => {
    loadAgents();
  }, []);

  // Filter agents when search query changes
  useEffect(() => {
    if (!searchQuery) {
      setFilteredAgents(agents);
      return;
    }

    const filtered = agents.filter(agent =>
      agent.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (agent.description && agent.description.toLowerCase().includes(searchQuery.toLowerCase()))
    );

    setFilteredAgents(filtered);
  }, [searchQuery, agents]);

  // Load agents from API
  const loadAgents = async () => {
    setIsLoading(true);
    try {
      const agentList = await getAgents();
      setAgents(agentList);
      setFilteredAgents(agentList);
    } catch (error) {
      console.error('Error loading agents:', error);
      toast({
        title: 'Error',
        description: 'Failed to load agents',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle create new agent
  const handleCreateAgent = () => {
    navigate('/agent-builder/new');
  };

  // Handle edit agent
  const handleEditAgent = (id: string) => {
    navigate(`/agent-builder/${id}`);
  };

  // Handle run agent
  const handleRunAgent = (id: string) => {
    setSelectedAgentId(id);
    setIsRunDialogOpen(true);
  };

  // Handle delete agent
  const handleDeleteClick = (agent: Agent) => {
    setAgentToDelete(agent);
    setIsDeleteDialogOpen(true);
  };

  // Confirm delete agent
  const confirmDelete = async () => {
    if (!agentToDelete?.id) return;

    try {
      await deleteAgent(agentToDelete.id);

      // Update local state
      setAgents(prevAgents => prevAgents.filter(agent => agent.id !== agentToDelete.id));

      toast({
        title: 'Success',
        description: 'Agent deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting agent:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete agent',
        variant: 'destructive'
      });
    } finally {
      setAgentToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Handle initial agent creation
  const handleInitialCreate = async () => {
    if (!newAgentName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter an agent name',
        variant: 'destructive'
      });
      return;
    }

    setIsCreating(true);
    try {
      const newAgent = await saveAgent({
        name: newAgentName,
        description: newAgentDescription,
        configuration: {
          blocks: [],
          entryBlockId: null
        }
      });

      toast({
        title: 'Success',
        description: 'Agent created successfully'
      });

      // Navigate to the builder with the new agent
      navigate(`/agent-builder/${newAgent.id}`);
    } catch (error) {
      console.error('Error creating agent:', error);
      toast({
        title: 'Error',
        description: 'Failed to create agent',
        variant: 'destructive'
      });
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white">
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-4">
            <WelcomeHeading
              text="Trading Agents"
              className="gradient-text text-2xl font-medium font-sans tracking-tight leading-tight"
              speed={80}
            />
          </div>
          <UniversalButton
            onClick={handleCreateAgent}
            variant="pill"
            size="default"
          >
            Build Agent
          </UniversalButton>
        </div>

        {showCreateForm ? (
          <div className="max-w-2xl mx-auto">
            <Card className="bg-[#141414]/40 border border-[#1A1A1A]/20 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-white/90 flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-white/70" />
                  Create New Agent
                </CardTitle>
                <CardDescription className="text-white/50">
                  Start by giving your agent a name and description
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="agentName" className="text-sm font-medium text-white/70">
                    Agent Name
                  </label>
                  <Input
                    id="agentName"
                    value={newAgentName}
                    onChange={(e) => setNewAgentName(e.target.value)}
                    placeholder="Enter a name for your agent"
                    className="bg-[#0A0A0A]/50 border-[#1A1A1A]/40 text-white placeholder:text-white/30 focus:border-[#232323]/60 focus:ring-0"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="agentDescription" className="text-sm font-medium text-white/70">
                    Description
                  </label>
                  <Textarea
                    id="agentDescription"
                    value={newAgentDescription}
                    onChange={(e) => setNewAgentDescription(e.target.value)}
                    placeholder="Describe what your agent will do"
                    className="bg-[#0A0A0A]/50 border-[#1A1A1A]/40 text-white placeholder:text-white/30 focus:border-[#232323]/60 focus:ring-0 min-h-[100px]"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-3 border-t border-[#1A1A1A]/20 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateForm(false)}
                  className="bg-transparent border-[#1A1A1A]/40 text-white/70 hover:bg-[#1A1A1A]/40 hover:text-white/90"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back
                </Button>
                <UniversalButton
                  onClick={handleInitialCreate}
                  loading={isCreating}
                  loadingText="Creating..."
                  variant="success"
                  size="default"
                  animation="shimmer"
                  className="font-sans tracking-tight leading-tight"
                >
                  <Settings className="h-4 w-4" />
                  Continue to Builder
                </UniversalButton>
              </CardFooter>
            </Card>
          </div>
        ) : (
          <>
            <div className="mb-6">
              <div className="relative max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-white/50" />
                <Input
                  placeholder="Search agents..."
                  className="bg-[#0A0A0A]/50 border-[#1A1A1A]/40 text-white placeholder:text-white/30 focus:border-[#232323]/60 focus:ring-0 pl-10 h-9 text-sm"
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                />
              </div>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center h-64">
                <Loader2 className="h-8 w-8 animate-spin text-white/70" />
              </div>
            ) : filteredAgents.length === 0 ? (
              <div className="text-center py-16 bg-[#141414]/40 border border-[#1A1A1A]/20 rounded-xl shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm">
                <h2 className="text-xl font-semibold mb-2 text-white/90">No agents found</h2>
                <p className="text-white/50 mb-6">
                  {searchQuery ? 'No agents match your search query' : 'You haven\'t created any agents yet'}
                </p>
                <UniversalButton
                  onClick={handleCreateAgent}
                  variant="pill"
                  size="default"
                >
                  Build Agent
                </UniversalButton>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredAgents.map(agent => (
                  <Card key={agent.id} className="overflow-hidden bg-[#141414]/40 border border-[#1A1A1A]/20 hover:border-[#232323]/40 transition-all duration-200 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm group flex flex-col h-full">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-white/90 text-lg">{agent.name}</CardTitle>
                        <div className="flex items-center gap-1.5">
                          {/* Public/Private indicator */}
                          <div className={`text-xs font-medium px-2 py-1 rounded-full ${
                            agent.is_public
                              ? 'bg-green-500/10 text-green-400 border border-green-500/20'
                              : 'bg-red-500/10 text-red-400 border border-red-500/20'
                          }`}>
                            {agent.is_public ? 'Public' : 'Private'}
                          </div>
                          <div className="text-xs text-white/40 bg-[#1A1A1A]/40 px-3 py-1.5 rounded-full whitespace-nowrap">
                            {agent.configuration.blocks.length} blocks
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-white/40 hover:text-white/70 hover:bg-[#1A1A1A]/40"
                            onClick={() => handleDeleteClick(agent)}
                          >
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <CardDescription className="text-white/40 text-xs">
                        Created {agent.created_at ? formatDate(agent.created_at) : 'Recently'}
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="flex-1 py-2">
                      {/* Content area to push footer to bottom */}
                      <div className="text-white/50 text-sm">
                        {agent.description || 'No description available'}
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-between pt-2 border-t border-[#1A1A1A]/20 mt-auto">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRunAgent(agent.id!)}
                        className="bg-[#1A1A1C]/50 border-[#1A1A1A]/40 text-white/70 hover:bg-[#1F1F1F]/90 hover:text-white/90 transition-all duration-200 group-hover:border-[#232323]/60"
                      >
                        Test
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditAgent(agent.id!)}
                        className="bg-[#1A1A1C]/50 border-[#1A1A1A]/40 text-white/70 hover:bg-[#1F1F1F]/90 hover:text-white/90 transition-all duration-200 group-hover:border-[#232323]/60"
                      >
                        Edit
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            )}
          </>
        )}

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent className="bg-[#141414]/90 border border-[#1A1A1A]/20 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] backdrop-blur-sm">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-white/90">Are you sure?</AlertDialogTitle>
              <AlertDialogDescription className="text-white/60">
                This will permanently delete the agent "{agentToDelete?.name}". This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="bg-[#1A1A1C]/50 border-[#1A1A1A]/40 text-white/70 hover:bg-[#1F1F1F]/90 hover:text-white/90 transition-all duration-200">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmDelete}
                className="bg-red-900/20 text-red-400 border border-red-500/20 hover:bg-red-900/30 hover:text-red-300 transition-all duration-200"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Run Dialog */}
        <AgentRunDialog
          open={isRunDialogOpen}
          onOpenChange={setIsRunDialogOpen}
          agentId={selectedAgentId || undefined}
        />
      </div>
    </div>
  );
};

export default AgentManagement;
