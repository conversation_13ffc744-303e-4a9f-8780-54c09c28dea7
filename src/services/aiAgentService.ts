import { supabase } from '@/integrations/supabase/client';
import { AgentBlock } from './agentService';

export interface AIAgentRequest {
  description: string;
  userId: string;
}

export interface AIAgentResponse {
  success: boolean;
  agent?: {
    name: string;
    description: string;
    blocks: Agent<PERSON><PERSON>[];
    entryBlockId: string;
  };
  error?: string;
  reasoning?: string;
}

/**
 * Generate an agent configuration using AI based on natural language description
 */
export async function generateAgentWithAI(request: AIAgentRequest): Promise<AIAgentResponse> {
  try {
    console.log('Generating agent with AI:', request);

    const { data, error } = await supabase.functions.invoke('ai-agent-generator', {
      body: {
        description: request.description,
        userId: request.userId,
        timestamp: new Date().toISOString()
      }
    });

    if (error) {
      console.error('AI agent generation error:', error);
      return {
        success: false,
        error: error.message || 'Failed to generate agent with AI'
      };
    }

    if (!data || !data.success) {
      console.error('AI agent generation failed:', data);
      return {
        success: false,
        error: data?.error || 'AI failed to generate a valid agent configuration'
      };
    }

    console.log('AI agent generation successful:', data);
    return {
      success: true,
      agent: data.agent,
      reasoning: data.reasoning
    };

  } catch (error) {
    console.error('Error in generateAgentWithAI:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Validate that an AI-generated agent configuration is valid
 */
export function validateAIGeneratedAgent(agent: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check required fields
  if (!agent.name || typeof agent.name !== 'string') {
    errors.push('Agent must have a valid name');
  }

  if (!agent.blocks || !Array.isArray(agent.blocks)) {
    errors.push('Agent must have a blocks array');
  }

  if (!agent.entryBlockId || typeof agent.entryBlockId !== 'string') {
    errors.push('Agent must have a valid entry block ID');
  }

  // Check blocks structure
  if (agent.blocks) {
    const blockIds = new Set();
    let hasWhenRunBlock = false;
    let hasTriggerBlock = false;

    for (const block of agent.blocks) {
      // Check for duplicate IDs
      if (blockIds.has(block.id)) {
        errors.push(`Duplicate block ID: ${block.id}`);
      }
      blockIds.add(block.id);

      // Check required block fields
      if (!block.id || !block.type || !block.position) {
        errors.push(`Block ${block.id || 'unknown'} is missing required fields`);
      }

      // Check block types
      if (block.type === 'when_run') {
        hasWhenRunBlock = true;
      }
      if (block.type === 'signal') {
        hasTriggerBlock = true;
      }

      // Validate position
      if (block.position && (typeof block.position.x !== 'number' || typeof block.position.y !== 'number')) {
        errors.push(`Block ${block.id} has invalid position`);
      }
    }

    // Check for required block types
    if (!hasWhenRunBlock) {
      errors.push('Agent must have at least one when_run block');
    }

    // Check for at least one final block (trigger or confidence boost)
    const hasConfidenceBoostBlock = agent.blocks.some((block: any) =>
      block.type === 'BULLISH_CONFIDENCE_BOOST' ||
      block.type === 'BEARISH_CONFIDENCE_BOOST' ||
      block.type === 'CONFIDENCE_BOOST' ||
      block.type === 'bullish_confidence_boost' ||
      block.type === 'bearish_confidence_boost' ||
      block.type === 'confidence_boost'
    );

    // Agents no longer require trigger blocks if they have confidence boost blocks
    if (!hasTriggerBlock && !hasConfidenceBoostBlock) {
      errors.push('Agent must have at least one final block (signal or confidence boost)');
    }

    // Check that entry block exists
    if (!blockIds.has(agent.entryBlockId)) {
      errors.push('Entry block ID does not exist in blocks array');
    }
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Transform AI-generated block types to the correct format
 */
function transformBlockType(aiBlock: any): any {
  const block = { ...aiBlock };

  // Convert lowercase types to uppercase enum values
  switch (block.type?.toLowerCase()) {
    case 'when_run':
      block.type = 'WHEN_RUN';
      break;
    case 'indicator':
      block.type = 'INDICATOR';
      // Ensure indicator has required properties
      if (!block.indicator) {
        block.indicator = 'rsi'; // Default indicator
      }
      if (!block.parameters) {
        block.parameters = { period: 14 };
      }
      break;
    case 'price':
      block.type = 'PRICE';
      // Ensure price has required properties
      if (!block.dataPoint) {
        block.dataPoint = 'close'; // Default data point
      }
      break;
    case 'fundamental':
      block.type = 'FUNDAMENTAL';
      // Ensure fundamental has required properties
      if (!block.metric) {
        block.metric = 'return_on_equity'; // Default metric
      }
      if (!block.statement) {
        block.statement = 'calculated'; // Default for ratios
      }
      if (!block.period) {
        block.period = 'quarterly'; // Default period
      }
      break;
    case 'condition':
      block.type = 'CONDITION';
      // Ensure condition has required properties
      if (!block.operator) {
        block.operator = '>'; // Default operator
      }
      if (block.compareValue === undefined || block.compareValue === null) {
        block.compareValue = 0; // Default compare value
      }
      break;
    case 'trigger':
      block.type = 'TRIGGER';
      // Ensure trigger has required properties
      if (!block.signal) {
        block.signal = 'bullish'; // Default signal
      }
      if (!block.confidence) {
        block.confidence = 75; // Default confidence
      }
      break;
    case 'operator':
      block.type = 'OPERATOR';
      // Ensure operator has required properties
      if (!block.operation) {
        block.operation = 'add'; // Default operation
      }
      break;
    case 'bullish_confidence_boost':
      block.type = 'BULLISH_CONFIDENCE_BOOST';
      // Ensure confidence boost has required properties
      if (!block.percentage) {
        block.percentage = 10; // Default percentage boost
      }
      break;
    case 'bearish_confidence_boost':
      block.type = 'BEARISH_CONFIDENCE_BOOST';
      // Ensure confidence boost has required properties
      if (!block.percentage) {
        block.percentage = 10; // Default percentage boost
      }
      break;
    case 'candle_pattern':
      block.type = 'CANDLE_PATTERN';
      // Ensure candle pattern has required properties
      if (!block.pattern) {
        block.pattern = 'doji'; // Default pattern
      }
      if (!block.timeframe) {
        block.timeframe = 'day'; // Default timeframe
      }
      break;
    default:
      // If AI generated a specific indicator name as type, convert it
      if (['rsi', 'sma', 'ema', 'macd', 'bollinger_bands', 'stochastic', 'williams_r', 'cci', 'atr', 'adx'].includes(block.type)) {
        block.indicator = block.type;
        block.type = 'INDICATOR';
        if (!block.parameters) {
          block.parameters = { period: 14 };
        }
      }
      break;
  }

  return block;
}

/**
 * Convert AI-generated agent to the format expected by the agent builder
 */
export function formatAIAgentForBuilder(aiAgent: any): {
  blocks: AgentBlock[];
  entryBlockId: string;
  name: string;
  description: string;
} {
  // Transform and format all blocks
  const formattedBlocks = aiAgent.blocks.map((block: any) => {
    const transformedBlock = transformBlockType(block);

    return {
      ...transformedBlock,
      // Ensure position is properly formatted
      position: {
        x: transformedBlock.position?.x || 0,
        y: transformedBlock.position?.y || 0
      },
      // Ensure connections are arrays
      inputConnections: transformedBlock.inputConnections || [],
      outputConnections: transformedBlock.outputConnections || [],
      // Add any missing properties with defaults
      parameters: transformedBlock.parameters || {},
    };
  });

  return {
    blocks: formattedBlocks,
    entryBlockId: aiAgent.entryBlockId,
    name: aiAgent.name,
    description: aiAgent.description || ''
  };
}
