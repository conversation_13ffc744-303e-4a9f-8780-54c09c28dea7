export interface ChartViewport {
  xMin: number;
  xMax: number;
  yMin: number;
  yMax: number;
  zoomLevel: number;
}

export interface ChartInteractionState {
  viewport: ChartViewport;
  isDragging: boolean;
  lastMousePosition: { x: number; y: number } | null;
  dragStartPosition: { x: number; y: number } | null;
  lastPanTime: number;
  panVelocity: { x: number; y: number };
}

// Zoom configuration for smooth, predictable behavior
const ZOOM_CONFIG = {
  ZOOM_FACTOR_IN: 1.05,    // Very gradual zoom in (5% per scroll)
  ZOOM_FACTOR_OUT: 0.95,   // Very gradual zoom out (5% per scroll)
  MIN_ZOOM_LEVEL: 0.01,    // Can zoom out to show 100x the original range
  MAX_ZOOM_LEVEL: 50,      // Can zoom in to show 1/50th of original range
  SMOOTH_FACTOR: 0.8       // Smoothing factor for zoom transitions
};

// Pan configuration for smooth movement
const PAN_CONFIG = {
  MOMENTUM_DECAY: 0.95,    // How quickly pan momentum decays
  MIN_VELOCITY: 0.1,       // Minimum velocity before stopping momentum
  MAX_VELOCITY: 100        // Maximum pan velocity
};

/**
 * Initialize chart interaction state with infinite viewport
 */
export function initializeInfiniteChartState(
  centerTime: number,
  initialTimeRange: number,
  priceMin: number,
  priceMax: number
): ChartInteractionState {
  // Add 5% padding to Y-axis for better visualization
  const yPadding = (priceMax - priceMin) * 0.05;

  return {
    viewport: {
      xMin: centerTime - initialTimeRange / 2,
      xMax: centerTime + initialTimeRange / 2,
      yMin: priceMin - yPadding,
      yMax: priceMax + yPadding,
      zoomLevel: 1
    },
    isDragging: false,
    lastMousePosition: null,
    dragStartPosition: null,
    lastPanTime: Date.now(),
    panVelocity: { x: 0, y: 0 }
  };
}

/**
 * Calculate optimal price range for given data
 */
export function calculatePriceRange(data: Array<{ high: number; low: number }>): { min: number; max: number } {
  if (data.length === 0) {
    return { min: 100, max: 200 }; // Default range
  }

  const prices = data.flatMap(d => [d.high, d.low]);
  return {
    min: Math.min(...prices),
    max: Math.max(...prices)
  };
}

/**
 * Handle smooth mouse wheel zoom events for infinite chart
 */
export function handleSmoothZoom(
  state: ChartInteractionState,
  event: WheelEvent,
  canvasRect: DOMRect,
  initialTimeRange: number
): ChartInteractionState {
  // preventDefault is now handled in the component with onWheelCapture

  // Use smooth, gradual zoom factors
  const zoomFactor = event.deltaY > 0 ? ZOOM_CONFIG.ZOOM_FACTOR_OUT : ZOOM_CONFIG.ZOOM_FACTOR_IN;
  const mouseX = event.clientX - canvasRect.left;
  const mouseY = event.clientY - canvasRect.top;

  // Account for chart margins
  const margin = { top: 20, right: 60, bottom: 40, left: 20 };
  const chartWidth = canvasRect.width - margin.left - margin.right;
  const chartHeight = canvasRect.height - margin.top - margin.bottom;

  // Only zoom if mouse is within chart area
  if (mouseX < margin.left || mouseX > margin.left + chartWidth ||
      mouseY < margin.top || mouseY > margin.top + chartHeight) {
    return state;
  }

  // Convert mouse position to data coordinates (relative to chart area)
  const relativeX = (mouseX - margin.left) / chartWidth;
  const relativeY = (mouseY - margin.top) / chartHeight;

  const dataX = state.viewport.xMin + relativeX * (state.viewport.xMax - state.viewport.xMin);
  const dataY = state.viewport.yMax - relativeY * (state.viewport.yMax - state.viewport.yMin);

  // Calculate current ranges
  const currentXRange = state.viewport.xMax - state.viewport.xMin;
  const currentYRange = state.viewport.yMax - state.viewport.yMin;

  // Calculate new ranges with zoom factor
  const newXRange = currentXRange * zoomFactor;
  const newYRange = currentYRange * zoomFactor;

  // Calculate zoom level relative to initial range
  const newZoomLevel = initialTimeRange / newXRange;

  // Constrain zoom level to reasonable bounds
  if (newZoomLevel < ZOOM_CONFIG.MIN_ZOOM_LEVEL || newZoomLevel > ZOOM_CONFIG.MAX_ZOOM_LEVEL) {
    return state; // Don't zoom if it would exceed limits
  }

  // Calculate new bounds centered on mouse position
  let newXMin = dataX - (dataX - state.viewport.xMin) * zoomFactor;
  let newXMax = dataX + (state.viewport.xMax - dataX) * zoomFactor;
  const newYMin = dataY - (dataY - state.viewport.yMin) * zoomFactor;
  const newYMax = dataY + (state.viewport.yMax - dataY) * zoomFactor;

  // Constrain to current time - cannot zoom into the future
  const currentTimestamp = Date.now();
  if (newXMax > currentTimestamp) {
    const xRange = newXMax - newXMin;
    newXMax = currentTimestamp;
    newXMin = newXMax - xRange;
  }

  return {
    ...state,
    viewport: {
      ...state.viewport,
      xMin: newXMin,
      xMax: newXMax,
      yMin: newYMin,
      yMax: newYMax,
      zoomLevel: newZoomLevel
    }
  };
}

/**
 * Handle mouse down for pan start
 */
export function handlePanStart(
  state: ChartInteractionState,
  event: MouseEvent,
  canvasRect: DOMRect
): ChartInteractionState {
  const mouseX = event.clientX - canvasRect.left;
  const mouseY = event.clientY - canvasRect.top;
  
  return {
    ...state,
    isDragging: true,
    lastMousePosition: { x: mouseX, y: mouseY },
    dragStartPosition: { x: mouseX, y: mouseY }
  };
}

/**
 * Handle smooth panning for infinite chart with current time boundary
 */
export function handleSmoothPan(
  state: ChartInteractionState,
  event: MouseEvent,
  canvasRect: DOMRect
): ChartInteractionState {
  if (!state.isDragging || !state.lastMousePosition) {
    return state;
  }

  const mouseX = event.clientX - canvasRect.left;
  const mouseY = event.clientY - canvasRect.top;

  const deltaX = mouseX - state.lastMousePosition.x;
  const deltaY = mouseY - state.lastMousePosition.y;
  const currentTime = Date.now();
  const deltaTime = Math.max(currentTime - state.lastPanTime, 1); // Prevent division by zero

  // Account for chart margins
  const margin = { top: 20, right: 60, bottom: 40, left: 20 };
  const chartWidth = canvasRect.width - margin.left - margin.right;
  const chartHeight = canvasRect.height - margin.top - margin.bottom;

  // Convert pixel deltas to data deltas (relative to chart area)
  const xRange = state.viewport.xMax - state.viewport.xMin;
  const yRange = state.viewport.yMax - state.viewport.yMin;

  const dataXDelta = -(deltaX / chartWidth) * xRange;
  const dataYDelta = (deltaY / chartHeight) * yRange;

  // Calculate velocity for momentum (pixels per millisecond)
  const velocityX = deltaX / deltaTime;
  const velocityY = deltaY / deltaTime;

  // Calculate new viewport bounds
  let newXMin = state.viewport.xMin + dataXDelta;
  let newXMax = state.viewport.xMax + dataXDelta;
  const newYMin = state.viewport.yMin + dataYDelta;
  const newYMax = state.viewport.yMax + dataYDelta;

  // Constrain to current time - cannot scroll into the future
  const currentTimestamp = Date.now();
  if (newXMax > currentTimestamp) {
    const overflow = newXMax - currentTimestamp;
    newXMax = currentTimestamp;
    newXMin = newXMax - xRange;
  }

  return {
    ...state,
    viewport: {
      ...state.viewport,
      xMin: newXMin,
      xMax: newXMax,
      yMin: newYMin,
      yMax: newYMax
    },
    lastMousePosition: { x: mouseX, y: mouseY },
    lastPanTime: currentTime,
    panVelocity: {
      x: Math.max(-PAN_CONFIG.MAX_VELOCITY, Math.min(PAN_CONFIG.MAX_VELOCITY, velocityX)),
      y: Math.max(-PAN_CONFIG.MAX_VELOCITY, Math.min(PAN_CONFIG.MAX_VELOCITY, velocityY))
    }
  };
}

/**
 * Handle pan end with momentum continuation
 */
export function handleSmoothPanEnd(state: ChartInteractionState): ChartInteractionState {
  return {
    ...state,
    isDragging: false,
    lastMousePosition: null,
    dragStartPosition: null,
    lastPanTime: Date.now()
    // Keep panVelocity for momentum effects if needed
  };
}

/**
 * Apply momentum to continue panning after mouse release
 */
export function applyPanMomentum(state: ChartInteractionState): ChartInteractionState {
  if (state.isDragging ||
      Math.abs(state.panVelocity.x) < PAN_CONFIG.MIN_VELOCITY &&
      Math.abs(state.panVelocity.y) < PAN_CONFIG.MIN_VELOCITY) {
    return state;
  }

  // Apply momentum decay
  const newVelocityX = state.panVelocity.x * PAN_CONFIG.MOMENTUM_DECAY;
  const newVelocityY = state.panVelocity.y * PAN_CONFIG.MOMENTUM_DECAY;

  // Convert velocity to data movement
  const xRange = state.viewport.xMax - state.viewport.xMin;
  const yRange = state.viewport.yMax - state.viewport.yMin;

  // Assume 60fps for smooth momentum
  const frameTime = 16.67; // milliseconds
  const dataXDelta = -(newVelocityX * frameTime / 1000) * xRange * 0.1; // Scale factor for smooth movement
  const dataYDelta = (newVelocityY * frameTime / 1000) * yRange * 0.1;

  return {
    ...state,
    viewport: {
      ...state.viewport,
      xMin: state.viewport.xMin + dataXDelta,
      xMax: state.viewport.xMax + dataXDelta,
      yMin: state.viewport.yMin + dataYDelta,
      yMax: state.viewport.yMax + dataYDelta
    },
    panVelocity: { x: newVelocityX, y: newVelocityY }
  };
}

/**
 * Reset viewport to show all data
 */
export function resetViewport(
  state: ChartInteractionState,
  dataXMin: number,
  dataXMax: number,
  dataYMin: number,
  dataYMax: number
): ChartInteractionState {
  const yPadding = (dataYMax - dataYMin) * 0.05;
  
  return {
    ...state,
    viewport: {
      xMin: dataXMin,
      xMax: dataXMax,
      yMin: dataYMin - yPadding,
      yMax: dataYMax + yPadding,
      zoomLevel: 1
    }
  };
}

/**
 * Convert data coordinates to canvas coordinates
 */
export function dataToCanvas(
  dataX: number,
  dataY: number,
  viewport: ChartViewport,
  canvasWidth: number,
  canvasHeight: number
): { x: number; y: number } {
  const x = ((dataX - viewport.xMin) / (viewport.xMax - viewport.xMin)) * canvasWidth;
  const y = canvasHeight - ((dataY - viewport.yMin) / (viewport.yMax - viewport.yMin)) * canvasHeight;
  
  return { x, y };
}

/**
 * Convert canvas coordinates to data coordinates
 */
export function canvasToData(
  canvasX: number,
  canvasY: number,
  viewport: ChartViewport,
  canvasWidth: number,
  canvasHeight: number
): { x: number; y: number } {
  const x = viewport.xMin + (canvasX / canvasWidth) * (viewport.xMax - viewport.xMin);
  const y = viewport.yMax - (canvasY / canvasHeight) * (viewport.yMax - viewport.yMin);

  return { x, y };
}

/**
 * Debounce function for API calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for high-frequency events
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Check if viewport has changed significantly enough to trigger data loading
 */
export function hasSignificantViewportChange(
  oldViewport: ChartViewport,
  newViewport: ChartViewport,
  threshold: number = 0.1
): boolean {
  const oldRange = oldViewport.xMax - oldViewport.xMin;
  const newRange = newViewport.xMax - newViewport.xMin;

  // Check if the viewport has moved or zoomed significantly
  const centerOld = (oldViewport.xMin + oldViewport.xMax) / 2;
  const centerNew = (newViewport.xMin + newViewport.xMax) / 2;
  const centerDiff = Math.abs(centerNew - centerOld);

  const rangeDiff = Math.abs(newRange - oldRange) / oldRange;
  const centerDiffRatio = centerDiff / oldRange;

  return rangeDiff > threshold || centerDiffRatio > threshold;
}
