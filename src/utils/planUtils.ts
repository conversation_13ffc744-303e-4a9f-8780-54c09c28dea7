// Plan utility functions for managing plan selection

// LocalStorage keys
export const PLAN_TYPE_KEY = 'Osis_selected_plan_type';

// Plan types
export const PLAN_TYPES = {
  BASIC: 'basic',
  PRO: 'pro',
  PREMIUM: 'premium'
};

// Utility function to determine if user was created before the cutoff date (6/3/25)
const isLegacyUser = (userCreatedAt: string): boolean => {
  const cutoffDate = new Date('2025-06-03T00:00:00Z');
  const userDate = new Date(userCreatedAt);
  return userDate < cutoffDate;
};

// Price IDs for different plans based on user creation date
// Legacy users (before 6/3/25) use pk_live_51Qq1bA... account with ROY price IDs
export const LEGACY_PLAN_PRICE_IDS = {
  [PLAN_TYPES.BASIC]: {
    weekly: 'price_1ROYLKDebmd1GpTvct491Kw6',    // $9.99
    yearly: 'price_1RVyr7Debmd1GpTvgWmmS7R1'     // $360
  },
  [PLAN_TYPES.PRO]: {
    weekly: 'price_1ROYKjDebmd1GpTv5oYNMKMv',    // $14.99
    yearly: 'price_1RVyqhDebmd1GpTvwoY0ridy'     // $540
  },
  [PLAN_TYPES.PREMIUM]: {
    weekly: 'price_1RVylIDebmd1GpTvXxDXQBwH',    // $19.99
    yearly: 'price_1RVypVDebmd1GpTvvh0jJVWT'     // $720
  }
};

// Current users (6/3/25+) use pk_live_51RRMm0... account with RVy price IDs
export const CURRENT_PLAN_PRICE_IDS = {
  [PLAN_TYPES.BASIC]: {
    weekly: 'price_1RVyDZFagIdq3bE61UyNGiAj',    // $9.99
    yearly: 'price_1RVyt2FagIdq3bE6Fb4d5OS2'     // $360
  },
  [PLAN_TYPES.PRO]: {
    weekly: 'price_1RVyE6FagIdq3bE6RP8yRJEv',    // $14.99
    yearly: 'price_1RVyE6FagIdq3bE6RP8yRJEv'     // $540
  },
  [PLAN_TYPES.PREMIUM]: {
    weekly: 'price_1RVyEvFagIdq3bE6TEMbZaUg',    // $19.99
    yearly: 'price_1RVysMFagIdq3bE6O30J3j9z'     // $720
  }
};

// Legacy price IDs for backward compatibility
export const PLAN_PRICE_IDS = {
  [PLAN_TYPES.BASIC]: 'price_1ROYLKDebmd1GpTvct491Kw6', // Default to current basic weekly
  [PLAN_TYPES.PRO]: 'price_1ROYKjDebmd1GpTv5oYNMKMv'    // Default to current pro weekly
};

/**
 * Set the selected plan type in localStorage
 * @param planType The plan type to store
 */
export const setSelectedPlanType = (planType: string): void => {
  try {
    localStorage.setItem(PLAN_TYPE_KEY, planType);
  } catch (error) {
    console.error('Error setting plan type in localStorage:', error);
  }
};

/**
 * Get the selected plan type from localStorage
 * @returns The stored plan type or null if not found
 */
export const getSelectedPlanType = (): string | null => {
  try {
    return localStorage.getItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error getting plan type from localStorage:', error);
    return null;
  }
};

/**
 * Clear the selected plan type from localStorage
 */
export const clearSelectedPlanType = (): void => {
  try {
    localStorage.removeItem(PLAN_TYPE_KEY);
  } catch (error) {
    console.error('Error clearing plan type from localStorage:', error);
  }
};

/**
 * Get the appropriate price IDs for a user based on their creation date
 * @param userCreatedAt The user's creation date
 * @returns The price ID configuration for the user
 */
export const getPriceIdsForUser = (userCreatedAt: string) => {
  return isLegacyUser(userCreatedAt) ? LEGACY_PLAN_PRICE_IDS : CURRENT_PLAN_PRICE_IDS;
};

/**
 * Get the price ID for a given plan type and billing period
 * @param planType The plan type
 * @param billingPeriod The billing period ('weekly' or 'yearly')
 * @param userCreatedAt The user's creation date (optional, defaults to current config)
 * @returns The price ID for the plan type or the basic plan price ID as default
 */
export const getPriceIdForPlanType = (
  planType: string | null,
  billingPeriod: 'weekly' | 'yearly' = 'weekly',
  userCreatedAt?: string
): string => {
  console.log('🔍 getPriceIdForPlanType called with:', {
    planType,
    billingPeriod,
    userCreatedAt,
    hasUserCreatedAt: !!userCreatedAt
  });

  if (!planType) {
    planType = PLAN_TYPES.BASIC; // Default to basic plan
    console.log('📝 No planType provided, defaulting to:', planType);
  }

  // If userCreatedAt is provided, use the appropriate config
  if (userCreatedAt) {
    console.log('✅ userCreatedAt provided, using date-based logic');
    const isLegacy = isLegacyUser(userCreatedAt);
    const priceIds = getPriceIdsForUser(userCreatedAt);
    const selectedPriceId = priceIds[planType]?.[billingPeriod] || priceIds[PLAN_TYPES.BASIC][billingPeriod];

    console.log('📊 Date-based price ID selection:', {
      userCreatedAt,
      isLegacy,
      selectedConfig: isLegacy ? 'LEGACY' : 'CURRENT',
      planType,
      billingPeriod,
      selectedPriceId
    });

    return selectedPriceId;
  }

  // Fallback to legacy PLAN_PRICE_IDS for backward compatibility
  const fallbackPriceId = PLAN_PRICE_IDS[planType] || PLAN_PRICE_IDS[PLAN_TYPES.BASIC];
  console.log('⚠️ NO userCreatedAt provided - using LEGACY fallback:', {
    planType,
    fallbackPriceId,
    warning: 'This will use the wrong price ID for users created after 6/3/25!'
  });

  return fallbackPriceId;
};

/**
 * Get the legacy price ID for a given plan type (for backward compatibility)
 * @param planType The plan type
 * @returns The price ID for the plan type or the basic plan price ID as default
 */
export const getLegacyPriceIdForPlanType = (planType: string | null): string => {
  if (!planType) {
    return PLAN_PRICE_IDS[PLAN_TYPES.BASIC]; // Default to basic plan
  }

  return PLAN_PRICE_IDS[planType] || PLAN_PRICE_IDS[PLAN_TYPES.BASIC];
};
