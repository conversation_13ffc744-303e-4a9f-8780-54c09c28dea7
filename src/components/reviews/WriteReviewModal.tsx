import React, { useState } from 'react';
import { Star, PenTool } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import StarRating from '@/components/ui/star-rating';
import { createAgentReview, updateAgentReview, AgentReview } from '@/services/reviewService';
import { useToast } from '@/components/ui/use-toast';

interface WriteReviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  agentName: string;
  existingReview?: AgentReview;
  backtestPerformance?: any;
  onSuccess?: () => void;
}

const WriteReviewModal: React.FC<WriteReviewModalProps> = ({
  isOpen,
  onClose,
  agentId,
  agentName,
  existingReview,
  backtestPerformance,
  onSuccess
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    rating: existingReview?.rating || 0,
    title: existingReview?.title || '',
    review_text: existingReview?.review_text || ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.rating === 0) {
      toast({
        variant: "destructive",
        title: "Rating Required",
        description: "Please select a star rating"
      });
      return;
    }

    if (!formData.review_text.trim()) {
      toast({
        variant: "destructive",
        title: "Review Required",
        description: "Please write a review"
      });
      return;
    }

    try {
      setLoading(true);

      const reviewData = {
        agent_id: agentId,
        rating: formData.rating,
        title: formData.title.trim() || undefined,
        review_text: formData.review_text.trim(),
        backtest_performance: backtestPerformance
      };

      if (existingReview) {
        await updateAgentReview(existingReview.id, reviewData);
        toast({
          title: "Review Updated",
          description: "Your review has been updated successfully"
        });
      } else {
        await createAgentReview(reviewData);
        toast({
          title: "Review Posted",
          description: "Thank you for your review!"
        });
      }

      onSuccess?.();
      onClose();
    } catch (error: any) {
      console.error('Error submitting review:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to submit review"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({
        rating: existingReview?.rating || 0,
        title: existingReview?.title || '',
        review_text: existingReview?.review_text || ''
      });
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="bg-[#1A1A1A] border-white/[0.08] text-white max-w-md max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <PenTool className="w-4 h-4 text-blue-400" />
            {existingReview ? 'Edit Review' : 'Write Review'}
          </DialogTitle>
          <DialogDescription className="text-white/60 text-sm">
            Share your experience with "{agentName}"
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Rating */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Rating *
            </label>
            <div className="flex items-center gap-2">
              <StarRating
                rating={formData.rating}
                interactive
                onRatingChange={(rating) => setFormData(prev => ({ ...prev, rating }))}
                size="lg"
              />
              <span className="text-white/60 text-sm ml-2">
                {formData.rating === 0 ? 'Select rating' : 
                 formData.rating === 1 ? 'Poor' :
                 formData.rating === 2 ? 'Fair' :
                 formData.rating === 3 ? 'Good' :
                 formData.rating === 4 ? 'Very Good' : 'Excellent'}
              </span>
            </div>
          </div>

          {/* Title */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Review Title (Optional)
            </label>
            <Input
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="Summarize your experience"
              className="bg-white/[0.02] border-white/[0.08] text-white placeholder:text-white/40 focus:border-white/[0.15] h-9"
              maxLength={200}
            />
          </div>

          {/* Review Text */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-white">
              Your Review *
            </label>
            <Textarea
              value={formData.review_text}
              onChange={(e) => setFormData(prev => ({ ...prev, review_text: e.target.value }))}
              placeholder="Tell others about your experience with this agent. How did it perform? Would you recommend it?"
              rows={4}
              className="bg-white/[0.02] border-white/[0.08] text-white placeholder:text-white/40 focus:border-white/[0.15] resize-none"
              maxLength={2000}
            />
            <div className="text-xs text-white/50 text-right">
              {formData.review_text.length}/2000
            </div>
          </div>

          {/* Backtest Performance Preview */}
          {backtestPerformance && (
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-3">
              <div className="text-sm font-medium text-blue-400 mb-2">
                Backtest Performance (will be included)
              </div>
              <div className="grid grid-cols-2 gap-2 text-xs">
                {backtestPerformance.total_return && (
                  <div>
                    <span className="text-white/60">Total Return:</span>
                    <span className={`ml-1 font-medium ${
                      backtestPerformance.total_return > 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {backtestPerformance.total_return > 0 ? '+' : ''}{backtestPerformance.total_return.toFixed(2)}%
                    </span>
                  </div>
                )}
                {backtestPerformance.sharpe_ratio && (
                  <div>
                    <span className="text-white/60">Sharpe Ratio:</span>
                    <span className="ml-1 font-medium text-white">
                      {backtestPerformance.sharpe_ratio.toFixed(2)}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex gap-2 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              className="flex-1 border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04] h-9"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white h-9"
              disabled={loading || formData.rating === 0}
            >
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>{existingReview ? 'Updating...' : 'Posting...'}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Star className="w-4 h-4" />
                  <span>{existingReview ? 'Update Review' : 'Post Review'}</span>
                </div>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default WriteReviewModal;
