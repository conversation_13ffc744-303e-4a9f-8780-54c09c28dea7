import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Star, Download, Calendar, User, Tag, Eye, MoreVertical, DollarSign, ShoppingCart, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { formatDistanceToNow } from 'date-fns';
import { type MarketplaceAgent } from '@/services/marketplaceService';
import { useResponsive } from '@/hooks/useResponsive';
import PurchaseModal from './PurchaseModal';
import AgentPricingModal from './AgentPricingModal';
import AgentDetailsModal from './AgentDetailsModal';
import StarRating from '@/components/ui/star-rating';
import { getAgentReviewSummary, ReviewSummary } from '@/services/reviewService';
import { supabase } from '@/integrations/supabase/client';

interface MarketplaceAgentCardProps {
  agent: MarketplaceAgent;
  viewMode: 'grid' | 'list';
  onPurchase: (agentId: string) => void;
  purchasing: boolean;
  onPricingUpdate?: () => void;
  onBacktest?: (agent: MarketplaceAgent) => void;
}

const MarketplaceAgentCard: React.FC<MarketplaceAgentCardProps> = ({
  agent,
  viewMode,
  onPurchase,
  purchasing,
  onPricingUpdate,
  onBacktest
}) => {
  const { isMobile } = useResponsive();
  const [showPricingModal, setShowPricingModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [reviewSummary, setReviewSummary] = useState<ReviewSummary | null>(null);

  // Get current user
  React.useEffect(() => {
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
    };
    getCurrentUser();
  }, []);

  // Load review summary
  useEffect(() => {
    const loadReviewSummary = async () => {
      try {
        const summary = await getAgentReviewSummary(agent.id);
        setReviewSummary(summary);
      } catch (error) {
        console.error('Error loading review summary:', error);
      }
    };
    loadReviewSummary();
  }, [agent.id]);

  const isOwner = currentUser && agent.user_id === currentUser.id;

  // Debug ownership
  React.useEffect(() => {
    console.log('Agent ownership check:', {
      agentName: agent.name,
      agentUserId: agent.user_id,
      currentUserId: currentUser?.id,
      isOwner
    });
  }, [currentUser, agent, isOwner]);

  const handlePurchase = async () => {
    console.log('Purchase button clicked for agent:', agent.name);

    try {
      // Call the purchase API to create Stripe checkout session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/marketplace-purchase`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          action: 'purchase-agent',
          agent_id: agent.id
        })
      });

      const data = await response.json();

      if (data.success && data.checkout_url) {
        // Redirect to Stripe Checkout
        window.location.href = data.checkout_url;
      } else {
        alert('Error creating checkout session: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      console.error('Purchase error:', error);
      alert('Failed to start purchase process');
    }
  };

  const handleSetPrice = () => {
    console.log('Set price button clicked for agent:', agent.name);
    setShowPricingModal(true);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating)
            ? 'text-yellow-400 fill-current'
            : i < rating
            ? 'text-yellow-400 fill-current opacity-50'
            : 'text-gray-600'
        }`}
      />
    ));
  };

  if (viewMode === 'list') {
    return (
      <Card className="bg-gradient-to-br from-[#0D0D0D] via-[#111111] to-[#0A0A0A] border border-white/[0.08] hover:border-white/[0.15] transition-all duration-300 hover:from-[#111111] hover:via-[#141414] hover:to-[#0D0D0D] shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_8px_32px_rgba(0,0,0,0.4)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.15),0_12px_40px_rgba(0,0,0,0.5)] rounded-xl backdrop-blur-sm">
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-start gap-4">
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="text-xl font-semibold text-white" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, sans-serif' }}>{agent.name}</h3>
                    <span className="text-lg font-bold text-green-400">{formatPrice(agent.price)}</span>
                  </div>
                  <p className="text-white/60 text-sm mb-3 line-clamp-2" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}>
                    {agent.description || 'No description available'}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-white/40 mb-3">
                    <div className="flex items-center gap-1">
                      <User className="w-3 h-3" />
                      <span>{agent.seller_name || 'Unknown'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ShoppingCart className="w-3 h-3" />
                      <span>{agent.sales_count} sales</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDistanceToNow(new Date(agent.created_at), { addSuffix: true })}</span>
                    </div>
                  </div>
                  {agent.tags && agent.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-3">
                      {agent.tags.slice(0, 3).map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                          {tag}
                        </Badge>
                      ))}
                      {agent.tags.length > 3 && (
                        <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                          +{agent.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end gap-3 ml-4">
              <Button
                onClick={handlePurchase}
                disabled={purchasing}
                className="bg-green-600 hover:bg-green-700 text-white border-0 shadow-[inset_0_1px_0_rgba(255,255,255,0.2),0_4px_12px_rgba(34,197,94,0.3)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_6px_16px_rgba(34,197,94,0.4)] transition-all duration-300 font-medium px-6 py-3 h-11 rounded-lg"
                style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
              >
                {purchasing ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Purchasing...</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="w-4 h-4" />
                    <span>Purchase</span>
                  </div>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-to-br from-[#0D0D0D] via-[#111111] to-[#0A0A0A] border border-white/[0.08] hover:border-white/[0.15] transition-all duration-300 hover:from-[#111111] hover:via-[#141414] hover:to-[#0D0D0D] shadow-[inset_0_1px_0_rgba(255,255,255,0.1),0_8px_32px_rgba(0,0,0,0.4)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.15),0_12px_40px_rgba(0,0,0,0.5)] rounded-xl h-full flex flex-col backdrop-blur-sm">
      <CardHeader className={`${isMobile ? 'pb-2 px-3 pt-3' : 'pb-3'}`}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className={`flex items-center gap-2 ${isMobile ? 'mb-1' : 'mb-2'}`}>
              <CardTitle className={`${isMobile ? 'text-sm' : 'text-lg'} text-white line-clamp-1`}>{agent.name}</CardTitle>
            </div>
            <div className="mb-2">
              <span className={`${isMobile ? 'text-sm' : 'text-base'} font-bold text-green-400`}>
                {formatPrice(agent.price)}
              </span>
            </div>
            <CardDescription className={`${isMobile ? 'text-xs' : 'text-sm'} text-white/60 line-clamp-2`}>
              {agent.description || 'No description available'}
            </CardDescription>

            {/* Rating Display */}
            {reviewSummary && reviewSummary.total_reviews > 0 && (
              <div className="flex items-center gap-2 mt-2">
                <StarRating rating={reviewSummary.average_rating} size="sm" />
                <span className="text-white/60 text-xs">
                  ({reviewSummary.total_reviews})
                </span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className={`${isMobile ? 'px-3 pb-3' : ''} flex-1 flex flex-col`}>
        <div className="flex-1">
          <div className={`flex items-center gap-3 text-xs text-white/40 ${isMobile ? 'mb-2' : 'mb-3'}`}>
            <div className="flex items-center gap-1">
              <User className="w-3 h-3" />
              <span className="truncate">{agent.seller_name || 'Unknown'}</span>
            </div>
            <div className="flex items-center gap-1">
              <ShoppingCart className="w-3 h-3" />
              <span>{agent.sales_count}</span>
            </div>
          </div>
          
          {agent.tags && agent.tags.length > 0 && (
            <div className={`flex flex-wrap gap-1 ${isMobile ? 'mb-2' : 'mb-3'}`}>
              {agent.tags.slice(0, isMobile ? 2 : 3).map((tag, index) => (
                <Badge key={index} variant="secondary" className="bg-white/5 text-white/60 text-xs px-1.5 py-0.5">
                  {tag}
                </Badge>
              ))}
              {agent.tags.length > (isMobile ? 2 : 3) && (
                <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs px-1.5 py-0.5">
                  +{agent.tags.length - (isMobile ? 2 : 3)}
                </Badge>
              )}
            </div>
          )}
        </div>
        
        <div className="mt-auto space-y-2">
          {/* View Details Button */}
          <Button
            onClick={() => setShowDetailsModal(true)}
            variant="outline"
            className={`w-full bg-gradient-to-br from-white/[0.06] to-white/[0.03] border border-white/[0.12] text-white/80 hover:text-white hover:from-white/[0.08] hover:to-white/[0.04] hover:border-white/[0.2] transition-all duration-300 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.15),0_4px_12px_rgba(0,0,0,0.2)] font-medium rounded-lg backdrop-blur-sm ${isMobile ? 'text-xs py-2 h-9' : 'h-10'}`}
            style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
          >
            <div className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              <span>View Details & Reviews</span>
            </div>
          </Button>

          {isOwner ? (
            // Show pricing controls for owner
            <Button
              onClick={handleSetPrice}
              className={`w-full bg-blue-600 hover:bg-blue-700 text-white border-0 shadow-[inset_0_1px_0_rgba(255,255,255,0.2),0_4px_12px_rgba(59,130,246,0.3)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_6px_16px_rgba(59,130,246,0.4)] transition-all duration-300 font-medium rounded-lg ${isMobile ? 'text-xs py-2 h-9' : 'h-10'}`}
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
            >
              <div className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                <span>{agent.is_for_sale ? 'Edit Listing' : 'List Agent'}</span>
              </div>
            </Button>
          ) : (
            // Show purchase button for others
            <Button
              onClick={handlePurchase}
              disabled={purchasing}
              className={`w-full bg-green-600 hover:bg-green-700 text-white border-0 shadow-[inset_0_1px_0_rgba(255,255,255,0.2),0_4px_12px_rgba(34,197,94,0.3)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_6px_16px_rgba(34,197,94,0.4)] transition-all duration-300 font-medium rounded-lg ${isMobile ? 'text-xs py-2 h-9' : 'h-10'}`}
              style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif' }}
            >
              {purchasing ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  <span>Purchasing...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <ShoppingCart className="w-4 h-4" />
                  <span>Purchase</span>
                </div>
              )}
            </Button>
          )}
        </div>
      </CardContent>



      {/* Agent Details Modal */}
      <AgentDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        agent={agent}
        onPurchase={(selectedAgent) => onPurchase?.(selectedAgent.id)}
        onBacktest={onBacktest}
        purchasing={purchasing}
      />

      {/* Pricing Modal for Owners */}
      {isOwner && (
        <AgentPricingModal
          isOpen={showPricingModal}
          onClose={() => setShowPricingModal(false)}
          agentId={agent.id}
          agentName={agent.name}
          currentPrice={agent.price}
          currentIsForSale={agent.is_for_sale}
          onSuccess={() => {
            setShowPricingModal(false);
            onPricingUpdate?.();
          }}
        />
      )}
    </Card>
  );
};

export default MarketplaceAgentCard;
