import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/components/ui/use-toast';
import { DollarSign, TrendingUp, AlertCircle, List } from 'lucide-react';
import { updateAgentPricing, removeAgentFromAllMarketplaces } from '@/services/marketplaceService';
import { publishAgent, unpublishAgent } from '@/services/discoverService';
import { supabase } from '@/integrations/supabase/client';
import { getAgentById } from '@/services/agentService';

interface AgentPricingModalProps {
  isOpen: boolean;
  onClose: () => void;
  agentId: string;
  agentName: string;
  currentPrice?: number | null;
  currentIsForSale?: boolean;
  onSuccess?: () => void;
}

const AgentPricingModal: React.FC<AgentPricingModalProps> = ({
  isOpen,
  onClose,
  agentId,
  agentName,
  currentPrice = 0,
  currentIsForSale = false,
  onSuccess
}) => {
  const { toast } = useToast();
  const [price, setPrice] = useState(currentPrice && currentPrice > 0 ? currentPrice.toString() : '');
  const [isForSale, setIsForSale] = useState(currentIsForSale);
  const [isPaid, setIsPaid] = useState(currentPrice && currentPrice > 0);
  const [loading, setLoading] = useState(false);

  // Additional state for free agent publishing
  const [agentData, setAgentData] = useState<any>(null);
  const [category, setCategory] = useState('General');
  const [description, setDescription] = useState('');

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setPrice(currentPrice && currentPrice > 0 ? currentPrice.toString() : '');
      setIsForSale(currentIsForSale || false);
      setIsPaid(currentPrice && currentPrice > 0);
      setCategory('General');
      setDescription('');
    }
  }, [isOpen, currentPrice, currentIsForSale]);

  // Load agent data when modal opens
  useEffect(() => {
    if (isOpen && agentId) {
      const loadAgentData = async () => {
        try {
          const agent = await getAgentById(agentId);
          setAgentData(agent);
          setDescription(agent?.description || '');
        } catch (error) {
          console.error('Error loading agent data:', error);
        }
      };
      loadAgentData();
    }
  }, [isOpen, agentId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const priceValue = isPaid ? parseFloat(price) : 0;

    // Validation
    if (isForSale && isPaid && (!price || priceValue <= 0)) {
      toast({
        variant: "destructive",
        title: "Invalid Price",
        description: "Please enter a valid price greater than $0"
      });
      return;
    }

    if (isForSale && isPaid && priceValue > 1000) {
      toast({
        variant: "destructive",
        title: "Price Too High",
        description: "Maximum price is $1,000"
      });
      return;
    }

    setLoading(true);

    try {
      // Get current user
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('User not authenticated');
      }

      if (!isForSale) {
        // COMPLETE MARKETPLACE REMOVAL: Remove from ALL marketplace sections
        console.log('🗑️ Removing agent from ALL marketplaces...');

        try {
          // First try to remove from free marketplace
          const unpublishResponse = await unpublishAgent(agentId);
          if (!unpublishResponse.success) {
            console.warn('⚠️ Failed to unpublish from free marketplace:', unpublishResponse.error);
          }

          // Then remove from paid marketplace
          const { error: paidRemovalError } = await supabase
            .from('agents')
            .update({
              is_for_sale: false,
              price: null,
              is_public: false,
              updated_at: new Date().toISOString()
            })
            .eq('id', agentId)
            .eq('user_id', user.id);

          if (paidRemovalError) {
            throw new Error(`Failed to remove from paid marketplace: ${paidRemovalError.message}`);
          }

          toast({
            title: "Success",
            description: "Agent removed from all marketplace sections!"
          });
          onSuccess?.();
          onClose();
        } catch (error) {
          throw new Error(error.message || 'Failed to remove agent from marketplace');
        }
      } else if (isForSale && !isPaid) {
        // FREE AGENT: Use publish system (published_agents table)
        // First ensure it's not in paid marketplace (but don't make it private)
        console.log('🆓 Publishing agent as free...');

        // Remove from paid marketplace only (keep agent public)
        const { error: paidRemovalError } = await supabase
          .from('agents')
          .update({
            is_for_sale: false,
            price: null,
            updated_at: new Date().toISOString()
          })
          .eq('id', agentId)
          .eq('user_id', user.id);

        if (paidRemovalError) {
          console.warn('⚠️ Failed to remove from paid marketplace:', paidRemovalError);
        }

        const publishResponse = await publishAgent({
          agentId: agentId,
          name: agentName,
          description: description || agentData?.description || '',
          category: category,
          tags: agentData?.tags || []
        });

        if (publishResponse.success) {
          toast({
            title: "Success",
            description: "Agent published as free and made public in marketplace!"
          });
          onSuccess?.();
          onClose();
        } else {
          throw new Error(publishResponse.error || 'Failed to publish agent');
        }
      } else {
        // PAID AGENT: Use marketplace system (agents table)
        // First ensure it's not in free marketplace
        await unpublishAgent(agentId);

        const response = await updateAgentPricing(agentId, {
          price: priceValue,
          is_for_sale: true
        });

        if (response.success) {

          let description = "";
          if (!isForSale) {
            description = "Agent removed from marketplace";
          } else if (isPaid) {
            description = `Agent listed for sale at $${priceValue.toFixed(2)} and made public in marketplace!`;
          }

          toast({
            title: "Success",
            description
          });
          onSuccess?.();
          onClose();
        } else {
          throw new Error(response.error || 'Failed to update pricing');
        }
      }
    } catch (error) {
      console.error('Error updating agent:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to update agent"
      });
    } finally {
      setLoading(false);
    }
  };

  const calculateEarnings = () => {
    const priceValue = parseFloat(price) || 0;
    const platformFee = priceValue * 0.15; // 15% platform fee
    const earnings = priceValue - platformFee;
    return { platformFee, earnings };
  };

  const { platformFee, earnings } = calculateEarnings();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="bg-[#1A1A1A] border-white/[0.08] text-white max-w-md max-h-[85vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg font-semibold text-white flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-green-400" />
            List Agent
          </DialogTitle>
          <DialogDescription className="text-white/60 text-sm">
            Configure listing for "{agentName}"
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* List for Sale Toggle */}
          <div className="flex items-center justify-between p-4 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] rounded-xl shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] backdrop-blur-sm">
            <div className="flex-1">
              <Label htmlFor="for-sale" className="text-sm font-medium text-white">
                List in Marketplace
              </Label>
              <p className="text-xs text-white/60 mt-0.5">
                Make public and available
              </p>
            </div>
            <Switch
              id="for-sale"
              checked={isForSale}
              onCheckedChange={setIsForSale}
            />
          </div>

          {/* Pricing Type Selection */}
          {isForSale && (
            <div className="space-y-3">
              <Label className="text-sm font-medium text-white">
                Pricing Model
              </Label>

              {/* Free Option */}
              <div
                className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 ${
                  !isPaid
                    ? 'border-green-500/50 bg-gradient-to-br from-green-500/10 to-green-500/5 shadow-[inset_0_1px_0_rgba(34,197,94,0.2)]'
                    : 'border-white/[0.12] bg-gradient-to-br from-white/[0.04] to-white/[0.02] hover:border-white/[0.2] hover:bg-white/[0.06] shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                }`}
                onClick={() => setIsPaid(false)}
              >
                <div className="flex items-center gap-2">
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                    !isPaid ? 'border-green-500 bg-green-500 shadow-[0_0_8px_rgba(34,197,94,0.4)]' : 'border-white/40'
                  }`}>
                    {!isPaid && <div className="w-2 h-2 bg-white rounded-full" />}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-white">Free Agent</div>
                    <div className="text-xs text-white/60">
                      Share for free to build reputation
                    </div>
                  </div>
                </div>
              </div>

              {/* Paid Option */}
              <div
                className={`p-4 border rounded-xl cursor-pointer transition-all duration-200 ${
                  isPaid
                    ? 'border-green-500/50 bg-gradient-to-br from-green-500/10 to-green-500/5 shadow-[inset_0_1px_0_rgba(34,197,94,0.2)]'
                    : 'border-white/[0.12] bg-gradient-to-br from-white/[0.04] to-white/[0.02] hover:border-white/[0.2] hover:bg-white/[0.06] shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]'
                }`}
                onClick={() => setIsPaid(true)}
              >
                <div className="flex items-center gap-2">
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                    isPaid ? 'border-green-500 bg-green-500 shadow-[0_0_8px_rgba(34,197,94,0.4)]' : 'border-white/40'
                  }`}>
                    {isPaid && <div className="w-2 h-2 bg-white rounded-full" />}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-white">Paid Agent</div>
                    <div className="text-xs text-white/60">
                      Set a price and earn money
                    </div>
                  </div>
                </div>
              </div>

              {/* Price Input for Paid Option */}
              {isPaid && (
                <div className="space-y-1 ml-5">
                  <Label htmlFor="price" className="text-xs font-medium text-white/60">
                    Price (USD)
                  </Label>
                  <div className="relative">
                    <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-white/40" />
                    <Input
                      id="price"
                      type="number"
                      value={price}
                      onChange={(e) => setPrice(e.target.value)}
                      placeholder="0.00"
                      min="1"
                      max="1000"
                      step="0.01"
                      className="pl-7 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] h-9 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
                      required={isPaid}
                    />
                  </div>
                  <p className="text-xs text-white/50">
                    Range: $1 - $1,000
                  </p>
                </div>
              )}

              {/* Category and Description for Free Agents */}
              {!isPaid && (
                <div className="space-y-3 ml-5">
                  <div className="space-y-1">
                    <Label htmlFor="category" className="text-xs font-medium text-white/60">
                      Category
                    </Label>
                    <Select value={category} onValueChange={setCategory}>
                      <SelectTrigger className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white h-9 text-sm rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200">
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent className="bg-[#1A1A1A] border-white/[0.08]">
                        <SelectItem value="General">General</SelectItem>
                        <SelectItem value="Day Trading">Day Trading</SelectItem>
                        <SelectItem value="Technical Analysis">Technical Analysis</SelectItem>
                        <SelectItem value="Options Trading">Options Trading</SelectItem>
                        <SelectItem value="Swing Trading">Swing Trading</SelectItem>
                        <SelectItem value="Risk Management">Risk Management</SelectItem>
                        <SelectItem value="Market Scanning">Market Scanning</SelectItem>
                        <SelectItem value="Fundamental Analysis">Fundamental Analysis</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-1">
                    <Label htmlFor="description" className="text-xs font-medium text-white/60">
                      Description
                    </Label>
                    <Textarea
                      id="description"
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder="Describe what your agent does..."
                      className="bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white placeholder:text-white/40 focus:border-white/[0.2] focus:bg-white/[0.06] text-sm resize-none rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
                      rows={3}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Earnings Breakdown */}
          {isForSale && isPaid && price && parseFloat(price) > 0 && (
            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
              <div className="flex items-center gap-2 text-xs font-medium text-green-400 mb-2">
                <TrendingUp className="w-3 h-3" />
                Earnings
              </div>

              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-white/60">Price:</span>
                  <span className="text-white">${parseFloat(price).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-white/60">Fee (15%):</span>
                  <span className="text-red-400">-${platformFee.toFixed(2)}</span>
                </div>
                <div className="border-t border-green-500/20 pt-1">
                  <div className="flex justify-between font-medium">
                    <span className="text-white">You earn:</span>
                    <span className="text-green-400">${earnings.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Info Box */}
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-2">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-3 h-3 text-blue-400 mt-0.5" />
              <div className="text-xs text-blue-200">
                <p className="font-medium mb-1">💡 Tips:</p>
                <ul className="space-y-0.5 text-blue-200/80">
                  <li>• Listing makes your agent public</li>
                  <li>• Research similar agents first</li>
                  <li>• Start with competitive pricing</li>
                  <li>• You can adjust pricing anytime</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 pt-3">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              className="flex-1 bg-gradient-to-br from-white/[0.04] to-white/[0.02] border border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.06] hover:border-white/[0.2] h-9 text-sm font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] transition-all duration-200"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white h-9 text-sm font-medium rounded-lg shadow-[inset_0_1px_0_rgba(255,255,255,0.2),0_4px_12px_rgba(34,197,94,0.3)] hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.3),0_6px_16px_rgba(34,197,94,0.4)] transition-all duration-200"
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center gap-1">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white"></div>
                  <span>Saving...</span>
                </div>
              ) : isForSale ? (
                <div className="flex items-center gap-1">
                  {isPaid ? (
                    <DollarSign className="w-3 h-3" />
                  ) : (
                    <List className="w-3 h-3" />
                  )}
                  <span>{isPaid ? 'List for Sale' : 'List as Free'}</span>
                </div>
              ) : (
                <span>Remove from Marketplace</span>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AgentPricingModal;
