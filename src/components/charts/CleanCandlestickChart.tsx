import React, { useRef, useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { FocusedOHLCVData } from '@/services/tradeAnalysisService';

interface TradeMarker {
  timestamp: number;
  price: number;
  type: 'entry' | 'exit';
  label: string;
  color: string;
  tradeIndex?: number; // Add trade index for navigation
}

interface CleanCandlestickChartProps {
  symbol: string;
  data: FocusedOHLCVData[];
  tradeMarkers: TradeMarker[];
  height?: number;
  className?: string;
  currentTradeIndex?: number;
  onNavigateToTrade?: (tradeIndex: number) => void;
  allTrades?: any[]; // All trades for timeline navigation
}

const CleanCandlestickChart: React.FC<CleanCandlestickChartProps> = ({
  symbol,
  data,
  tradeMarkers,
  height = 400,
  className = '',
  currentTradeIndex,
  onNavigateToTrade,
  allTrades = []
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height });
  const [hoveredCandle, setHoveredCandle] = useState<FocusedOHLCVData | null>(null);
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null);

  // Zoom and pan state
  const [zoomLevel, setZoomLevel] = useState(1);
  const [panOffset, setPanOffset] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });

  // Timeline navigation logic
  const canNavigatePrevious = currentTradeIndex !== undefined && currentTradeIndex > 0;
  const canNavigateNext = currentTradeIndex !== undefined && currentTradeIndex < allTrades.length - 1;

  const handlePreviousTrade = () => {
    if (canNavigatePrevious && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex - 1);
    }
  };

  const handleNextTrade = () => {
    if (canNavigateNext && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex + 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousTrade();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextTrade();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTradeIndex, canNavigatePrevious, canNavigateNext]);

  // Handle canvas resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height: height - 70 }); // Account for larger header
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [height]);

  // Calculate chart bounds from data with zoom and pan support
  const chartBounds = React.useMemo(() => {
    if (data.length === 0) {
      return {
        xMin: Date.now() - 24 * 60 * 60 * 1000,
        xMax: Date.now(),
        yMin: 100,
        yMax: 200
      };
    }

    const timestamps = data.map(d => d.timestamp);
    const prices = data.flatMap(d => [d.high, d.low]);

    // Include trade marker prices in bounds calculation, but filter out unreasonable ones
    const chartTimeRange = Math.max(...timestamps) - Math.min(...timestamps);
    const reasonableTradeMarkers = tradeMarkers.filter(marker => {
      const timeDiff = Math.abs(marker.timestamp - Math.min(...timestamps));
      return timeDiff <= chartTimeRange * 2; // Allow markers within 2x the chart range
    });

    const tradeMarkerPrices = reasonableTradeMarkers.map(m => m.price);
    const allPrices = [...prices, ...tradeMarkerPrices];

    let xMin = Math.min(...timestamps);
    let xMax = Math.max(...timestamps);
    let yMin = Math.min(...allPrices);
    let yMax = Math.max(...allPrices);

    // Apply zoom to the bounds
    const xRange = xMax - xMin;
    const yRange = yMax - yMin;

    const zoomedXRange = xRange / zoomLevel;
    const zoomedYRange = yRange / zoomLevel;

    const xCenter = (xMin + xMax) / 2;
    const yCenter = (yMin + yMax) / 2;

    // Apply pan offset (convert pixel offset to data units)
    const xPanOffset = (panOffset.x / canvasSize.width) * zoomedXRange;
    const yPanOffset = (panOffset.y / canvasSize.height) * zoomedYRange;

    xMin = xCenter - zoomedXRange / 2 - xPanOffset;
    xMax = xCenter + zoomedXRange / 2 - xPanOffset;
    yMin = yCenter - zoomedYRange / 2 + yPanOffset; // Y is inverted
    yMax = yCenter + zoomedYRange / 2 + yPanOffset;

    // Add 10% padding to Y-axis for better visibility
    const yPadding = (yMax - yMin) * 0.1;

    const bounds = {
      xMin,
      xMax,
      yMin: yMin - yPadding,
      yMax: yMax + yPadding
    };

    return bounds;
  }, [data, tradeMarkers, zoomLevel, panOffset, canvasSize]);

  // Handle zoom with mouse wheel
  const handleWheel = useCallback((event: WheelEvent) => {
    event.preventDefault();
    const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;
    setZoomLevel(prev => Math.max(0.5, Math.min(5, prev * zoomFactor)));
  }, []);

  // Handle mouse events for panning and hover
  const handleMouseDown = useCallback((event: MouseEvent) => {
    setIsDragging(true);
    setLastMousePos({ x: event.clientX, y: event.clientY });
  }, []);

  const handleNativeMouseMove = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    if (isDragging) {
      // Handle panning
      const deltaX = event.clientX - lastMousePos.x;
      const deltaY = event.clientY - lastMousePos.y;
      setPanOffset(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));
      setLastMousePos({ x: event.clientX, y: event.clientY });
    } else {
      // Handle hover for tooltip
      setMousePosition({ x, y });

      // Find hovered candle
      const margin = { top: 20, right: 80, bottom: 50, left: 20 };
      const chartWidth = canvasSize.width - margin.left - margin.right;
      const chartHeight = canvasSize.height - margin.top - margin.bottom;

      if (x >= margin.left && x <= margin.left + chartWidth &&
          y >= margin.top && y <= margin.top + chartHeight) {

        const timeRange = chartBounds.xMax - chartBounds.xMin;
        const adjustedX = (x - margin.left - panOffset.x) / zoomLevel;
        const timestamp = chartBounds.xMin + (adjustedX / chartWidth) * timeRange;

        // Find closest candle
        const closestCandle = data.reduce((closest, candle) => {
          const currentDiff = Math.abs(candle.timestamp - timestamp);
          const closestDiff = Math.abs(closest.timestamp - timestamp);
          return currentDiff < closestDiff ? candle : closest;
        });

        setHoveredCandle(closestCandle);
      } else {
        setHoveredCandle(null);
      }
    }
  }, [isDragging, lastMousePos, data, canvasSize, chartBounds, zoomLevel, panOffset]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
    setHoveredCandle(null);
    setMousePosition(null);
  }, []);

  // Add event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('wheel', handleWheel);
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleNativeMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleNativeMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [handleWheel, handleMouseDown, handleNativeMouseMove, handleMouseUp, handleMouseLeave]);



  // Draw the chart
  const drawChart = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || data.length === 0) return;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasSize.width * dpr;
    canvas.height = canvasSize.height * dpr;
    canvas.style.width = `${canvasSize.width}px`;
    canvas.style.height = `${canvasSize.height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas with dark background
    ctx.fillStyle = '#101010';
    ctx.fillRect(0, 0, canvasSize.width, canvasSize.height);

    // Chart margins
    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;

    // Grid removed for cleaner appearance

    // Draw candlesticks
    drawCandlesticks(ctx, margin, chartWidth, chartHeight);

    // Draw trade markers
    drawTradeMarkers(ctx, margin, chartWidth, chartHeight);

    // Draw axes
    drawYAxisLabels(ctx, margin, chartWidth, chartHeight);
    drawXAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Zoom level indicator removed for cleaner appearance

    // Draw tooltip if hovering
    if (hoveredCandle && mousePosition) {
      drawTooltip(ctx, hoveredCandle, mousePosition);
    }
  }, [data, canvasSize, tradeMarkers, hoveredCandle, mousePosition, chartBounds, zoomLevel]);

  // Grid function removed for cleaner chart appearance

  // Draw candlesticks
  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (data.length === 0) return;

    // Calculate optimal candle width based on available space and number of candles
    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const pixelsPerMs = chartWidth / timeRange;

    // Calculate optimal spacing and candle width for better visual appearance
    const totalCandles = data.length;
    const availableWidth = chartWidth;

    // Reserve space for gaps between candles (40% of total width for spacing)
    const spacingRatio = 0.4;
    const totalSpacingWidth = availableWidth * spacingRatio;
    const totalCandleWidth = availableWidth - totalSpacingWidth;

    // Calculate base candle width
    const baseCandleWidth = totalCandleWidth / totalCandles;

    // Set minimum and maximum candle widths for better appearance
    const minCandleWidth = 3; // Minimum 3px for visibility
    const maxCandleWidth = 20; // Maximum 20px to prevent overly thick candles

    // Apply constraints and ensure good visual balance
    let candleWidth = Math.max(minCandleWidth, Math.min(baseCandleWidth, maxCandleWidth));

    // Adjust for different data densities
    if (totalCandles > 100) {
      // Very dense data - use thinner candles
      candleWidth = Math.max(2, Math.min(candleWidth, 8));
    } else if (totalCandles < 10) {
      // Sparse data - ensure candles aren't too wide
      candleWidth = Math.max(minCandleWidth, Math.min(candleWidth, 15));
    }

    // Calculate actual spacing between candles
    const actualSpacing = (availableWidth - (candleWidth * totalCandles)) / (totalCandles + 1);

    console.log(`[Chart] Candle rendering:`, {
      dataPoints: totalCandles,
      chartWidth: availableWidth,
      candleWidth: candleWidth.toFixed(2),
      actualSpacing: actualSpacing.toFixed(2),
      spacingRatio: (spacingRatio * 100).toFixed(1) + '%'
    });

    // Always use evenly-spaced positioning for consistent appearance
    // This ensures no overlapping and professional look across all timeframes
    data.forEach((candle, index) => {
      // Calculate X position with consistent spacing
      const x = margin.left + actualSpacing + (index * (candleWidth + actualSpacing)) + (candleWidth / 2);
      
      const highY = margin.top + chartHeight - ((candle.high - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;
      const lowY = margin.top + chartHeight - ((candle.low - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;
      const openY = margin.top + chartHeight - ((candle.open - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;
      const closeY = margin.top + chartHeight - ((candle.close - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      const isGreen = candle.close >= candle.open;
      const bullishColor = '#22c55e'; // Brighter emerald-400 for better visibility
      const bearishColor = '#ef4444'; // Keep red
      const color = isGreen ? bullishColor : bearishColor;

      // Add bright outer glow effect
      ctx.shadowColor = color;
      ctx.shadowBlur = 12; // Much brighter glow
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // Draw wick (high-low line) with enhanced styling
      ctx.strokeStyle = color;
      ctx.lineWidth = Math.max(1.5, candleWidth * 0.15); // Slightly thicker wicks
      ctx.lineCap = 'round';
      ctx.beginPath();
      ctx.moveTo(x, highY);
      ctx.lineTo(x, lowY);
      ctx.stroke();

      // Draw body (open-close rectangle) with enhanced styling
      const bodyTop = Math.min(openY, closeY);
      const bodyHeight = Math.max(Math.abs(closeY - openY), 3); // Minimum 3px height for better visibility

      // Add stronger glow for the body
      ctx.shadowBlur = 4;

      // Fill the body with gradient for depth
      const gradient = ctx.createLinearGradient(x - candleWidth / 2, bodyTop, x + candleWidth / 2, bodyTop + bodyHeight);
      if (isGreen) {
        gradient.addColorStop(0, '#22c55e'); // Brighter emerald
        gradient.addColorStop(1, '#16a34a'); // Darker emerald
      } else {
        gradient.addColorStop(0, '#ef4444');
        gradient.addColorStop(1, '#dc2626');
      }

      ctx.fillStyle = gradient;
      ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);

      // Reset shadow for border
      ctx.shadowBlur = 0;

      // Add a crisp border for definition
      ctx.strokeStyle = isGreen ? '#16a34a' : '#b91c1c';
      ctx.lineWidth = 1;
      ctx.strokeRect(x - candleWidth / 2, bodyTop, candleWidth, bodyHeight);

      // For very small bodies (doji-like candles), ensure visibility with enhanced styling
      if (bodyHeight <= 3) {
        ctx.shadowColor = color;
        ctx.shadowBlur = 2;
        ctx.strokeStyle = color;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.moveTo(x - candleWidth / 2, bodyTop);
        ctx.lineTo(x + candleWidth / 2, bodyTop);
        ctx.stroke();
        ctx.shadowBlur = 0;
      }
    });
  };

  // Draw trade markers
  const drawTradeMarkers = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (tradeMarkers.length === 0) {
      console.log('[Chart] No trade markers to draw');
      return;
    }

    console.log(`[Chart] Drawing ${tradeMarkers.length} trade markers`);
    console.log(`[Chart] Chart bounds:`, chartBounds);

    const timeRange = chartBounds.xMax - chartBounds.xMin;

    tradeMarkers.forEach((marker, index) => {
      // Calculate position
      const x = margin.left + ((marker.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      console.log(`[Chart] Marker ${index}:`, {
        timestamp: new Date(marker.timestamp).toISOString(),
        price: marker.price,
        x: x,
        y: y,
        chartBounds: {
          xMin: new Date(chartBounds.xMin).toISOString(),
          xMax: new Date(chartBounds.xMax).toISOString(),
          yMin: chartBounds.yMin,
          yMax: chartBounds.yMax
        },
        timeRange: timeRange,
        timeDiff: marker.timestamp - chartBounds.xMin,
        timeRatio: (marker.timestamp - chartBounds.xMin) / timeRange,
        isInBounds: x >= margin.left && x <= margin.left + chartWidth && y >= margin.top && y <= margin.top + chartHeight
      });

      // Skip markers that are way outside the chart timeframe
      if (marker.timestamp < chartBounds.xMin - timeRange * 0.5 || marker.timestamp > chartBounds.xMax + timeRange * 0.5) {
        console.log(`[Chart] Skipping marker ${index} - outside reasonable timeframe`);
        return;
      }

      // Also skip if the calculated X position is way outside the chart area
      if (x < margin.left - 50 || x > margin.left + chartWidth + 50) {
        console.log(`[Chart] Skipping marker ${index} - X position too far outside chart (${x})`);
        return;
      }

      const isCurrentTrade = marker.tradeIndex === currentTradeIndex;
      const markerSize = isCurrentTrade ? 10 : 7; // Larger markers for better visibility
      const lineWidth = isCurrentTrade ? 4 : 2;

      // Use the same X coordinate for both line and circle to ensure alignment
      let finalX = x;
      let finalY = y;
      let isRepositioned = false;

      // Only reposition if way outside reasonable bounds
      if (isCurrentTrade && (x < margin.left - 100 || x > margin.left + chartWidth + 100)) {
        finalX = margin.left + chartWidth / 2;
        finalY = margin.top + chartHeight / 2;
        isRepositioned = true;
        console.log(`[Chart] Repositioning current trade marker to center due to timestamp mismatch`);
      }

      // Keep markers within chart bounds but don't clamp too aggressively
      const clampedX = Math.max(margin.left, Math.min(finalX, margin.left + chartWidth));
      const clampedY = Math.max(margin.top, Math.min(finalY, margin.top + chartHeight));

      // Draw clean glowing trade marker (no vertical line)
      if (!isRepositioned) {
        // Add glow effect for the trade marker
        ctx.shadowColor = marker.color;
        ctx.shadowBlur = isCurrentTrade ? 15 : 8;
        ctx.shadowOffsetX = 0;
        ctx.shadowOffsetY = 0;

        // Draw outer glow ring for current trade
        if (isCurrentTrade) {
          ctx.fillStyle = marker.color + '40'; // Add transparency
          ctx.beginPath();
          ctx.arc(clampedX, clampedY, markerSize + 6, 0, 2 * Math.PI);
          ctx.fill();
        }

        // Draw main marker circle with enhanced glow
        ctx.fillStyle = marker.color;
        ctx.beginPath();
        ctx.arc(clampedX, clampedY, markerSize, 0, 2 * Math.PI);
        ctx.fill();

        // Add white center dot for better visibility
        ctx.shadowBlur = 0;
        ctx.fillStyle = '#FFFFFF';
        ctx.beginPath();
        ctx.arc(clampedX, clampedY, markerSize * 0.3, 0, 2 * Math.PI);
        ctx.fill();

        // Reset shadow
        ctx.shadowColor = 'transparent';
        ctx.shadowBlur = 0;
      }

      // Labels removed for cleaner appearance

      // Add special indicator for repositioned markers
      if (isCurrentTrade && isRepositioned) {
        // Draw a pulsing border to indicate this is a repositioned marker
        ctx.strokeStyle = '#FFFFFF';
        ctx.lineWidth = 2;
        ctx.setLineDash([3, 3]);
        ctx.beginPath();
        ctx.arc(clampedX, clampedY, markerSize + 6, 0, 2 * Math.PI);
        ctx.stroke();
        ctx.setLineDash([]);
      }
    });
  };

  // Draw Y-axis price labels with proper positioning
  const drawYAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';

    // Draw 5 price levels for better spacing
    const priceStep = (chartBounds.yMax - chartBounds.yMin) / 4;
    for (let i = 0; i <= 4; i++) {
      const price = chartBounds.yMin + i * priceStep;
      const y = margin.top + chartHeight - ((price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      // Ensure labels are properly positioned within chart bounds
      if (y >= margin.top && y <= margin.top + chartHeight) {
        ctx.fillText(`$${price.toFixed(2)}`, margin.left + chartWidth + 8, y + 3);
      }
    }
  };

  // Draw X-axis time labels with proper positioning
  const drawXAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.6)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'center';

    // Draw 4 time labels for better spacing
    const timeStep = (chartBounds.xMax - chartBounds.xMin) / 3;
    for (let i = 0; i <= 3; i++) {
      const time = chartBounds.xMin + i * timeStep;
      const x = margin.left + ((time - chartBounds.xMin) / (chartBounds.xMax - chartBounds.xMin)) * chartWidth;

      // Ensure labels are properly positioned within chart bounds
      if (x >= margin.left && x <= margin.left + chartWidth) {
        const timeLabel = format(new Date(time), 'MMM dd HH:mm');
        ctx.fillText(timeLabel, x, margin.top + chartHeight + 18);
      }
    }
  };

  // Draw enhanced glassmorphism tooltip with clean styling
  const drawTooltip = (
    ctx: CanvasRenderingContext2D,
    candle: FocusedOHLCVData,
    position: { x: number; y: number }
  ) => {
    const tooltipWidth = 200;
    const tooltipHeight = 120; // Proper height for content
    let x = position.x + 15;
    let y = position.y - tooltipHeight - 15;

    // Adjust position to keep tooltip in bounds
    if (x + tooltipWidth > canvasSize.width) x = position.x - tooltipWidth - 15;
    if (y < 0) y = position.y + 15;

    // Enhanced glassmorphism background with depth
    const gradient = ctx.createLinearGradient(x, y, x, y + tooltipHeight);
    gradient.addColorStop(0, 'rgba(20, 20, 20, 0.95)');
    gradient.addColorStop(0.5, 'rgba(15, 15, 15, 0.98)');
    gradient.addColorStop(1, 'rgba(10, 10, 10, 0.95)');

    // Enhanced shadow for depth
    ctx.shadowColor = 'rgba(0, 0, 0, 0.6)';
    ctx.shadowBlur = 25;
    ctx.shadowOffsetX = 0;
    ctx.shadowOffsetY = 12;

    ctx.fillStyle = gradient;
    ctx.beginPath();
    ctx.roundRect(x, y, tooltipWidth, tooltipHeight, 20); // More radius
    ctx.fill();

    // Reset shadow
    ctx.shadowColor = 'transparent';
    ctx.shadowBlur = 0;

    // Enhanced border with glassmorphism effect
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 1.5;
    ctx.stroke();

    // Inner highlight for depth
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.08)';
    ctx.lineWidth = 0.5;
    ctx.beginPath();
    ctx.roundRect(x + 1, y + 1, tooltipWidth - 2, tooltipHeight - 2, 19);
    ctx.stroke();

    const padding = 12; // Proper padding for content
    let currentY = y + padding + 14;

    // Draw timestamp header with enhanced styling
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 12px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';
    ctx.fillText(`${format(new Date(candle.timestamp), 'MMM dd, yyyy HH:mm')}`, x + padding, currentY);

    currentY += 18; // Proper spacing

    // Draw OHLC data with enhanced formatting and spacing
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';

    // Open
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('Open', x + padding, currentY);
    ctx.fillStyle = '#ffffff';
    ctx.textAlign = 'right';
    ctx.fillText(`$${candle.open.toFixed(2)}`, x + tooltipWidth - padding, currentY);
    currentY += 16; // Proper spacing

    // High
    ctx.textAlign = 'left';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('High', x + padding, currentY);
    ctx.fillStyle = '#22c55e'; // Brighter green
    ctx.textAlign = 'right';
    ctx.fillText(`$${candle.high.toFixed(2)}`, x + tooltipWidth - padding, currentY);
    currentY += 16;

    // Low
    ctx.textAlign = 'left';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('Low', x + padding, currentY);
    ctx.fillStyle = '#ef4444';
    ctx.textAlign = 'right';
    ctx.fillText(`$${candle.low.toFixed(2)}`, x + tooltipWidth - padding, currentY);
    currentY += 16;

    // Close
    ctx.textAlign = 'left';
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.fillText('Close', x + padding, currentY);
    const closeColor = candle.close >= candle.open ? '#22c55e' : '#ef4444';
    ctx.fillStyle = closeColor;
    ctx.textAlign = 'right';
    ctx.fillText(`$${candle.close.toFixed(2)}`, x + tooltipWidth - padding, currentY);

    // No change/volume section - clean OHLC only
  };

  // Redraw chart when state changes
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  // Handle React mouse events for tooltip (simplified version)
  const handleReactMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    // The native event handler will handle most of the logic
    // This is just a fallback for React events
    if (isDragging) return;

    const rect = canvasRef.current?.getBoundingClientRect();
    if (!rect) return;

    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;
    setMousePosition({ x: mouseX, y: mouseY });
  };

  // Handle clicks on trade markers
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !onNavigateToTrade) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;
    const timeRange = chartBounds.xMax - chartBounds.xMin;

    // Check if click is near any trade marker
    tradeMarkers.forEach((marker) => {
      const x = margin.left + ((marker.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      // Check if click is within 15 pixels of the marker
      const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));
      if (distance <= 15 && marker.tradeIndex !== undefined) {
        onNavigateToTrade(marker.tradeIndex);
      }
    });
  };

  // Show error state if no data
  if (data.length === 0) {
    return (
      <div className={`w-full bg-[#101010] rounded-lg border border-red-500/20 ${className}`} style={{ height }}>
        <div className="p-3 border-b border-red-500/20">
          <h3 className="text-red-400 text-sm font-medium">{symbol} - Chart Data Error</h3>
        </div>
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-6">
            <div className="text-red-400 text-lg mb-2">📊 No Data Available</div>
            <div className="text-white/60 text-sm">
              Unable to load real market data for this symbol.
              <br />
              Please verify the symbol and try again.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`w-full bg-[#101010] rounded-lg border border-[#1A1A1C] ${className}`} style={{ height }}>
      {/* Header with navigation */}
      <div className="p-3 border-b border-[#1A1A1C] flex items-center justify-between">
        <div className="flex items-center gap-3">
          <h3 className="text-white text-sm font-medium">{symbol} - Trade Analysis</h3>
        </div>

        <div className="flex items-center gap-4">
          {/* Zoom Controls */}
          <div className="flex items-center gap-2">
            <span className="text-xs text-white/60">Zoom:</span>
            <div className="flex items-center gap-1">
              <button
                onClick={() => setZoomLevel(prev => Math.max(0.5, prev * 0.8))}
                className="px-2 py-1 text-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-colors"
                title="Zoom Out"
              >
                −
              </button>
              <span className="text-xs text-white/60 min-w-[40px] text-center">
                {zoomLevel.toFixed(1)}x
              </span>
              <button
                onClick={() => setZoomLevel(prev => Math.min(5, prev * 1.25))}
                className="px-2 py-1 text-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-colors"
                title="Zoom In"
              >
                +
              </button>
              <button
                onClick={() => {
                  setZoomLevel(1);
                  setPanOffset({ x: 0, y: 0 });
                }}
                className="px-2 py-1 text-xs text-white/70 hover:text-white hover:bg-white/10 rounded transition-colors"
                title="Reset View"
              >
                Reset
              </button>
            </div>
          </div>

          {/* Timeline Navigation Controls */}
          {allTrades.length > 1 && onNavigateToTrade && (
            <div className="flex items-center gap-1">
              <button
                onClick={handlePreviousTrade}
                disabled={!canNavigatePrevious}
                className={`p-1 rounded transition-colors ${
                  canNavigatePrevious
                    ? 'text-white/70 hover:text-white hover:bg-white/10'
                    : 'text-white/30 cursor-not-allowed'
                }`}
                title="Previous Trade (←)"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <button
                onClick={handleNextTrade}
                disabled={!canNavigateNext}
                className={`p-1 rounded transition-colors ${
                  canNavigateNext
                    ? 'text-white/70 hover:text-white hover:bg-white/10'
                    : 'text-white/30 cursor-not-allowed'
                }`}
                title="Next Trade (→)"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Chart Canvas */}
      <div className="relative" style={{ height: height - 70 }}>
        <canvas
          ref={canvasRef}
          className="cursor-crosshair w-full"
          style={{ height: height - 70 }}
          onMouseMove={handleReactMouseMove}
          onMouseLeave={handleMouseLeave}
          onClick={handleCanvasClick}
        />
      </div>
    </div>
  );
};

export default CleanCandlestickChart;
