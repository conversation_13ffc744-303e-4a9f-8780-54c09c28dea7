import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AgentBlock } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';
import { AlertCircle, CheckCircle2 } from 'lucide-react';

interface AgentPreviewProps {
  blocks: AgentBlock[];
  connections: any[];
  entryBlockId: string;
}

const AgentPreview: React.FC<AgentPreviewProps> = ({ blocks, connections, entryBlockId }) => {
  // Validate the agent
  const validateAgent = () => {
    const errors: string[] = [];
    const errorDetails: Record<string, string[]> = {};

    // Check if there are any blocks
    if (blocks.length === 0) {
      errors.push('Agent must have at least one block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if there is an entry block
    if (!entryBlockId) {
      errors.push('Agent must have an entry block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if the entry block exists
    if (!blocks.some(block => block.id === entryBlockId)) {
      errors.push('Entry block does not exist');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if there is at least one trigger block OR confidence boost block
    const triggerBlocks = blocks.filter(block => block.type === BlockType.TRIGGER);
    const confidenceBoostBlocks = blocks.filter(block =>
      block.type === BlockType.BULLISH_CONFIDENCE_BOOST ||
      block.type === BlockType.BEARISH_CONFIDENCE_BOOST ||
      block.type === BlockType.CONFIDENCE_BOOST
    );

    if (triggerBlocks.length === 0 && confidenceBoostBlocks.length === 0) {
      errors.push('Agent must have at least one trigger block or confidence boost block');
      return { valid: false, errors, errorDetails, disconnectedBlocks: [] };
    }

    // Check if all trigger blocks are connected (only if we have trigger blocks)
    if (triggerBlocks.length > 0) {
      const connectedTriggers = triggerBlocks.filter(block =>
        block.inputConnections && block.inputConnections.length > 0
      );

      if (connectedTriggers.length === 0) {
        errors.push('At least one trigger block must be connected to other blocks');
        triggerBlocks.forEach(block => {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Trigger block is not connected to any input');
        });
      }
    }

    // Check if all blocks are connected
    const connectedBlockIds = new Set<string>();
    connectedBlockIds.add(entryBlockId);

    // Helper function to recursively find connected blocks
    const findConnectedBlocks = (blockId: string) => {
      const block = blocks.find(b => b.id === blockId);
      if (!block) return;

      // Add output connections
      if (block.outputConnections) {
        block.outputConnections.forEach(targetId => {
          if (!connectedBlockIds.has(targetId)) {
            connectedBlockIds.add(targetId);
            findConnectedBlocks(targetId);
          }
        });
      }

      // Add true/false connections for condition blocks
      if (block.type === BlockType.CONDITION) {
        if (block.trueConnection && !connectedBlockIds.has(block.trueConnection)) {
          connectedBlockIds.add(block.trueConnection);
          findConnectedBlocks(block.trueConnection);
        }

        if (block.falseConnection && !connectedBlockIds.has(block.falseConnection)) {
          connectedBlockIds.add(block.falseConnection);
          findConnectedBlocks(block.falseConnection);
        }
      }

      // Add connections for blocks with multiple output terminals
      if (block.type === BlockType.CANDLE_PATTERN ||
          block.type === BlockType.STOCK_SENTIMENT ||
          block.type === BlockType.CHART_PATTERN) {
        if (block.trueConnection && !connectedBlockIds.has(block.trueConnection)) {
          connectedBlockIds.add(block.trueConnection);
          findConnectedBlocks(block.trueConnection);
        }

        if (block.falseConnection && !connectedBlockIds.has(block.falseConnection)) {
          connectedBlockIds.add(block.falseConnection);
          findConnectedBlocks(block.falseConnection);
        }

        if (block.bullishConnection && !connectedBlockIds.has(block.bullishConnection)) {
          connectedBlockIds.add(block.bullishConnection);
          findConnectedBlocks(block.bullishConnection);
        }

        if (block.bearishConnection && !connectedBlockIds.has(block.bearishConnection)) {
          connectedBlockIds.add(block.bearishConnection);
          findConnectedBlocks(block.bearishConnection);
        }

        if (block.neutralConnection && !connectedBlockIds.has(block.neutralConnection)) {
          connectedBlockIds.add(block.neutralConnection);
          findConnectedBlocks(block.neutralConnection);
        }
      }

      // Add connections for gap analysis blocks
      if (block.type === BlockType.GAP_ANALYSIS) {
        if (block.gapUpConnection && !connectedBlockIds.has(block.gapUpConnection)) {
          connectedBlockIds.add(block.gapUpConnection);
          findConnectedBlocks(block.gapUpConnection);
        }

        if (block.gapDownConnection && !connectedBlockIds.has(block.gapDownConnection)) {
          connectedBlockIds.add(block.gapDownConnection);
          findConnectedBlocks(block.gapDownConnection);
        }

        if (block.noGapConnection && !connectedBlockIds.has(block.noGapConnection)) {
          connectedBlockIds.add(block.noGapConnection);
          findConnectedBlocks(block.noGapConnection);
        }
      }
    };

    findConnectedBlocks(entryBlockId);

    const disconnectedBlocks = blocks.filter(block => !connectedBlockIds.has(block.id));

    // Add specific error messages for disconnected blocks
    disconnectedBlocks.forEach(block => {
      if (!errorDetails[block.id]) errorDetails[block.id] = [];
      errorDetails[block.id].push('Block is disconnected from the flow');
    });

    if (disconnectedBlocks.length > 0) {
      errors.push(`There are ${disconnectedBlocks.length} disconnected blocks`);
    }

    // Check if condition blocks have at least a true connection (false connection is optional)
    const conditionBlocks = blocks.filter(block => block.type === BlockType.CONDITION);
    conditionBlocks.forEach(block => {
      if (connectedBlockIds.has(block.id)) { // Only check connected condition blocks
        // Check both the block's trueConnection property AND the connections array for robustness
        const hasTrueConnectionInBlock = !!block.trueConnection;
        const hasTrueConnectionInArray = connections.some(conn =>
          conn.sourceId === block.id && conn.sourceHandle === 'true'
        );

        if (!hasTrueConnectionInBlock && !hasTrueConnectionInArray) {
          if (!errorDetails[block.id]) errorDetails[block.id] = [];
          errorDetails[block.id].push('Condition block is missing TRUE output connection');
          errors.push(`Condition block is missing TRUE output connection`);
        }

        // FALSE connection is now optional - no validation required
        // This allows for conditions where you only want to do something when true
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      errorDetails,
      disconnectedBlocks
    };
  };

  const { valid, errors, errorDetails, disconnectedBlocks } = validateAgent();

  // Generate a summary of the agent
  const generateSummary = () => {
    // Count blocks by type
    const blockCounts = blocks.reduce((counts, block) => {
      counts[block.type] = (counts[block.type] || 0) + 1;
      return counts;
    }, {} as Record<string, number>);

    // Get the entry block
    const entryBlock = blocks.find(block => block.id === entryBlockId);

    // Get trigger blocks
    const triggerBlocks = blocks.filter(block => block.type === BlockType.TRIGGER);

    return {
      blockCounts,
      entryBlock,
      triggerBlocks
    };
  };

  const summary = generateSummary();

  // Generate a description of what the agent does
  const generateDescription = () => {
    if (!valid) {
      return 'This agent is not valid and cannot be executed.';
    }

    let description = 'This agent ';

    // Describe the entry point
    if (summary.entryBlock) {
      switch (summary.entryBlock.type) {
        case BlockType.INDICATOR:
          description += `starts by analyzing the ${summary.entryBlock.indicator} indicator`;
          break;
        case BlockType.PRICE:
          description += `starts by examining the ${summary.entryBlock.dataPoint} price`;
          break;
        case BlockType.FUNDAMENTAL:
          description += `starts by evaluating the ${summary.entryBlock.metric} fundamental metric`;
          break;
        case BlockType.CONDITION:
          description += `starts with a condition check`;
          break;
        case BlockType.OPERATOR:
          description += `starts with a ${summary.entryBlock.operation} operation`;
          break;
        case BlockType.TRIGGER:
          description += `immediately generates a ${summary.entryBlock.signal} signal`;
          break;
      }
    }

    // Describe the number of conditions
    const conditionCount = summary.blockCounts[BlockType.CONDITION] || 0;
    if (conditionCount > 0) {
      description += ` and evaluates ${conditionCount} condition${conditionCount > 1 ? 's' : ''}`;
    }

    // Describe the indicators used
    const indicatorCount = summary.blockCounts[BlockType.INDICATOR] || 0;
    if (indicatorCount > 0) {
      const indicatorBlocks = blocks.filter(block => block.type === BlockType.INDICATOR);
      const indicators = indicatorBlocks.map(block => block.indicator);
      const uniqueIndicators = [...new Set(indicators)];

      description += `. It uses ${uniqueIndicators.join(', ')} for technical analysis`;
    }

    // Describe the fundamentals used
    const fundamentalCount = summary.blockCounts[BlockType.FUNDAMENTAL] || 0;
    if (fundamentalCount > 0) {
      const fundamentalBlocks = blocks.filter(block => block.type === BlockType.FUNDAMENTAL);
      const metrics = fundamentalBlocks.map(block => block.metric);
      const uniqueMetrics = [...new Set(metrics)];

      description += `. It considers ${uniqueMetrics.join(', ')} for fundamental analysis`;
    }

    // Describe the triggers
    if (summary.triggerBlocks.length > 0) {
      const signals = summary.triggerBlocks.map(block => block.signal);
      const uniqueSignals = [...new Set(signals)];

      description += `. It can generate ${uniqueSignals.join(' or ')} signals`;
    }

    description += '.';
    return description;
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Agent Preview</CardTitle>
        </CardHeader>
        <CardContent>
          {valid ? (
            <Alert variant="default" className="bg-green-50 border-green-200">
              <CheckCircle2 className="h-4 w-4 text-green-600" />
              <AlertTitle className="text-green-800">Valid Agent</AlertTitle>
              <AlertDescription className="text-green-700">
                This agent is valid and can be executed.
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Invalid Agent</AlertTitle>
              <AlertDescription>
                <div className="mt-2">
                  <h4 className="font-medium mb-1">General Issues:</h4>
                  <ul className="list-disc pl-5 mb-3">
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>

                  {Object.keys(errorDetails).length > 0 && (
                    <>
                      <h4 className="font-medium mb-1">Specific Block Issues:</h4>
                      <div className="space-y-2">
                        {Object.entries(errorDetails).map(([blockId, blockErrors]) => {
                          const block = blocks.find(b => b.id === blockId);
                          return (
                            <div key={blockId} className="pl-5">
                              <span className="font-medium">{block?.type} Block:</span>
                              <ul className="list-disc pl-5">
                                {blockErrors.map((error, i) => (
                                  <li key={i}>{error}</li>
                                ))}
                              </ul>
                            </div>
                          );
                        })}
                      </div>
                    </>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Agent Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{generateDescription()}</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Block Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Block Types</h3>
              <ul className="space-y-1">
                <li className="text-sm">
                  Indicators: {summary.blockCounts[BlockType.INDICATOR] || 0}
                </li>
                <li className="text-sm">
                  Price Data: {summary.blockCounts[BlockType.PRICE] || 0}
                </li>
                <li className="text-sm">
                  Fundamentals: {summary.blockCounts[BlockType.FUNDAMENTAL] || 0}
                </li>
                <li className="text-sm">
                  Conditions: {summary.blockCounts[BlockType.CONDITION] || 0}
                </li>
                <li className="text-sm">
                  Operators: {summary.blockCounts[BlockType.OPERATOR] || 0}
                </li>
                <li className="text-sm">
                  Triggers: {summary.blockCounts[BlockType.TRIGGER] || 0}
                </li>
                <li className="text-sm">
                  Confidence Boosts: {(summary.blockCounts[BlockType.BULLISH_CONFIDENCE_BOOST] || 0) + (summary.blockCounts[BlockType.BEARISH_CONFIDENCE_BOOST] || 0) + (summary.blockCounts[BlockType.CONFIDENCE_BOOST] || 0)}
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-medium mb-2">Connections</h3>
              <p className="text-sm">Total Connections: {connections.length}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AgentPreview;
