import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { DollarSign, Settings, Play, Edit, MoreVertical, ShoppingCart, Shield, Key, Lock, List } from 'lucide-react';
import AgentPricingModal from '@/components/marketplace/AgentPricingModal';
import { Agent, checkAgentOwnership, getAgentAccessLevel } from '@/services/agentService';
import { useToast } from '@/hooks/use-toast';

interface AgentCardProps {
  agent: Agent;
  onEdit: (agent: Agent) => void;
  onRun: (agent: Agent) => void;
  onDelete: (agent: Agent) => void;
  onRefresh: () => void;
}

const AgentCard: React.FC<AgentCardProps> = ({ agent, onEdit, onRun, onDelete, onRefresh }) => {
  const { toast } = useToast();
  const [showPricingModal, setShowPricingModal] = useState(false);

  // Use agent metadata instead of async checks
  const isOwned = agent.is_owned || false;
  const isLicensed = agent.is_licensed || agent.is_purchased || false;
  const canEdit = isOwned; // Only owners can edit
  const canSetPrice = isOwned; // Only owners can set prices
  const canDelete = isOwned; // Only owners can delete

  // Handle edit with ownership check
  const handleEdit = () => {
    if (!canEdit) {
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "You can only edit agents you created. Licensed agents cannot be modified."
      });
      return;
    }
    onEdit(agent);
  };

  // Handle pricing modal with ownership check
  const handleSetPrice = async () => {
    if (!canSetPrice) {
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "You can only set prices for agents you created, not licensed agents."
      });
      return;
    }
    setShowPricingModal(true);
  };

  // Handle delete with ownership check
  const handleDelete = () => {
    if (!canDelete) {
      toast({
        variant: "destructive",
        title: "Access Denied",
        description: "You can only delete agents you created, not licensed agents."
      });
      return;
    }
    onDelete(agent);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <>
      <Card className="bg-gradient-to-br from-white/[0.03] to-white/[0.01] border-white/[0.08] hover:border-white/[0.12] transition-all duration-300 hover:from-white/[0.04] hover:to-white/[0.02] hover:shadow-lg hover:shadow-black/20 h-full flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-lg text-white mb-2 line-clamp-1 flex items-center gap-2">
                {agent.name}
                {isLicensed && (
                  <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30 text-xs">
                    🔒 Licensed
                  </Badge>
                )}
              </CardTitle>
              <p className="text-white/60 text-sm line-clamp-2">
                {isLicensed
                  ? (agent.description || 'Licensed agent - secure execution only')
                  : (agent.description || 'No description available')
                }
              </p>
            </div>
            <div className="ml-2 flex flex-col gap-1">
              {/* Price Badge */}
              {agent.is_for_sale && agent.price && (
                <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                  {formatPrice(agent.price)}
                </Badge>
              )}
              {/* Sales Count */}
              {agent.sales_count > 0 && (
                <Badge variant="secondary" className="bg-blue-500/20 text-blue-400">
                  <ShoppingCart className="w-3 h-3 mr-1" />
                  {agent.sales_count} sales
                </Badge>
              )}
              {/* Public/Private Status */}
              <Badge 
                variant={agent.is_public ? "default" : "secondary"}
                className={agent.is_public 
                  ? "bg-green-500/20 text-green-400 border border-green-500/30" 
                  : "bg-red-500/20 text-red-400 border border-red-500/30"
                }
              >
                {agent.is_public ? 'Public' : 'Private'}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col">
          <div className="flex-1">
            {/* Agent Stats */}
            <div className="flex items-center gap-4 text-xs text-white/40 mb-3">
              <span>📅 {agent.created_at ? formatDate(agent.created_at) : 'Recently'}</span>
              <span>🧩 {agent.configuration?.blocks?.length || 0} blocks</span>
              {agent.likes_count > 0 && (
                <span>❤️ {agent.likes_count} likes</span>
              )}
            </div>

            {/* Tags */}
            {agent.tags && agent.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-3">
                {agent.tags.slice(0, 3).map((tag, index) => (
                  <Badge key={index} variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                    {tag}
                  </Badge>
                ))}
                {agent.tags.length > 3 && (
                  <Badge variant="secondary" className="bg-white/5 text-white/60 text-xs px-2 py-0.5">
                    +{agent.tags.length - 3}
                  </Badge>
                )}
              </div>
            )}
          </div>
          
          {/* Action Buttons */}
          <div className="flex gap-2 mt-auto pt-3 border-t border-white/[0.08]">
            <Button
              onClick={() => onRun(agent)}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              size="sm"
            >
              <Play className="w-4 h-4 mr-2" />
              Test
            </Button>

            {/* SECURITY: Only show Edit button for owned agents */}
            {canEdit ? (
              <Button
                onClick={handleEdit}
                variant="outline"
                className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
                size="sm"
                title="Edit agent configuration"
              >
                <Edit className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                variant="outline"
                className="border-orange-500/20 text-orange-400 hover:bg-orange-500/10"
                size="sm"
                title="Licensed agent - cannot be edited"
                disabled
              >
                <Lock className="w-4 h-4" />
              </Button>
            )}

            {/* SECURITY: Only show list button for owned agents */}
            {canSetPrice ? (
              <Button
                onClick={handleSetPrice}
                variant="outline"
                className="border-green-500/20 text-green-400 hover:bg-green-500/10"
                size="sm"
                title="List agent on marketplace"
              >
                {agent.is_for_sale && agent.price && agent.price > 0 ? (
                  <DollarSign className="w-4 h-4" />
                ) : (
                  <List className="w-4 h-4" />
                )}
              </Button>
            ) : (
              <Button
                variant="outline"
                className="border-blue-500/20 text-blue-400 hover:bg-blue-500/10"
                size="sm"
                title="Licensed agent - secure execution only"
                disabled
              >
                <Shield className="w-4 h-4" />
              </Button>
            )}

            {/* SECURITY: Only show delete button for owned agents */}
            {canDelete ? (
              <Button
                onClick={handleDelete}
                variant="outline"
                className="border-red-500/20 text-red-400 hover:bg-red-500/10"
                size="sm"
                title="Delete agent"
              >
                <MoreVertical className="w-4 h-4" />
              </Button>
            ) : (
              <Button
                variant="outline"
                className="border-gray-500/20 text-gray-400"
                size="sm"
                title="Licensed agent - cannot be deleted"
                disabled
              >
                <Shield className="w-4 h-4" />
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Pricing Modal */}
      <AgentPricingModal
        isOpen={showPricingModal}
        onClose={() => setShowPricingModal(false)}
        agentId={agent.id!}
        agentName={agent.name}
        currentPrice={agent.price}
        currentIsForSale={agent.is_for_sale}
        onSuccess={() => {
          setShowPricingModal(false);
          onRefresh();
        }}
      />
    </>
  );
};

export default AgentCard;
