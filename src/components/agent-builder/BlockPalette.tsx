import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Bar<PERSON>hart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap, Plus, Minus, BarChart3, Activity, LineChart, Crosshair, Terminal, Shield, Percent, Clock, Globe, Layers, Building, RotateCcw, PieChart, ArrowUpDown, Newspaper } from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  // Neutral detailed styling for all blocks
  const style = {
    gradient: 'from-white/[0.08] to-white/[0.02]',
    border: 'border-white/[0.15]',
    iconBg: 'bg-white/[0.08]',
    iconColor: 'text-white/90',
    glow: 'hover:shadow-[0_0_25px_rgba(255,255,255,0.12)]'
  };

  return (
    <div
      className="mb-1.5 cursor-grab rounded-lg transition-transform duration-200 hover:scale-[1.01] active:scale-95"
      draggable
      onDragStart={handleDragStart}
      onDragEnd={() => {
        // Subtle animation when dragged to canvas
        const element = document.querySelector(`[data-block-type="${type}"]`);
        if (element) {
          element.classList.add('animate-pulse');
          setTimeout(() => element.classList.remove('animate-pulse'), 300);
        }
      }}
    >
      <div className="p-2.5 flex items-center gap-2.5 border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent hover:border-white/[0.12] transition-colors duration-200">
        <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-md bg-white/[0.08] text-white/80 border border-white/[0.08]">
          <div className="w-3.5 h-3.5 flex items-center justify-center">
            {React.cloneElement(icon as React.ReactElement, {
              className: "w-3.5 h-3.5"
            })}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-xs font-medium text-white leading-tight">{title}</h3>
          <p className="text-[10px] text-white/60 leading-tight mt-0.5">{description}</p>
        </div>
      </div>
    </div>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <div className="space-y-4">
      <Accordion type="multiple" defaultValue={['data', 'technical', 'logic', 'signals', 'output', 'debugging']} className="space-y-3">
        <AccordionItem value="data" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-white/70" />
              Data Sources
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="technical" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <BarChart2 className="w-4 h-4 text-white/70" />
              Technical Analysis
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.INDICATOR}
            title="Technical Indicator"
            icon={<BarChart2 className="h-4 w-4" />}
            description="RSI, MACD, SMA, EMA, Bollinger Bands, Support/Resistance, etc."
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', parameters: { period: 14 } }}
          />
          <BlockItem
            type={BlockType.MOVING_AVERAGE}
            title="Moving Average"
            icon={<LineChart className="h-4 w-4" />}
            description="SMA, EMA, WMA with customizable periods"
            onBlockDrop={onBlockDrop}
            properties={{ averageType: 'sma', period: 20, timeframe: 'day', source: 'close' }}
          />
          <BlockItem
            type={BlockType.MOMENTUM_INDICATOR}
            title="Momentum Indicator"
            icon={<Activity className="h-4 w-4" />}
            description="RSI, Stochastic, Williams %R, CCI"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', period: 14, timeframe: 'day', overbought: 70, oversold: 30 }}
          />
          <BlockItem
            type={BlockType.CANDLE_PATTERN}
            title="Candle Pattern"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Doji, Hammer, Shooting Star, Marubozu, Engulfing, etc."
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.CHART_PATTERN}
            title="Chart Pattern"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Triangles, flags, head & shoulders, double tops/bottoms"
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.BREAKOUT_DETECTION}
            title="Breakout Detection"
            icon={<ArrowUpDown className="h-4 w-4" />}
            description="Support/resistance breakouts, trend line breaks"
            onBlockDrop={onBlockDrop}
            properties={{ breakoutType: 'support_resistance', timeframe: 'day', volumeConfirmation: false }}
          />
          <BlockItem
            type={BlockType.GAP_ANALYSIS}
            title="Gap Analysis"
            icon={<Crosshair className="h-4 w-4" />}
            description="Gap up/down detection with size thresholds"
            onBlockDrop={onBlockDrop}
            properties={{ gapType: 'any', minGapSize: 1, timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.STOCK_SENTIMENT}
            title="Stock Sentiment"
            icon={<Newspaper className="h-4 w-4" />}
            description="Analyzes news sentiment for bullish/bearish/neutral signals"
            onBlockDrop={onBlockDrop}
            properties={{ articleLimit: 10, daysBack: 7 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="logic" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <GitBranch className="w-4 h-4 text-white/70" />
              Logic & Processing
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.CONDITION}
            title="Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description=">, <, ==, >=, <=, !=, AND, OR, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.CANDLE_PATTERN}
            title="Candle Pattern"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Doji, Hammer, Shooting Star, Marubozu, Engulfing, Morning/Evening Star, etc."
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Add, Subtract, Multiply, Divide, Average, etc."
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="signals" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-white/70" />
              Signal Confidence
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.CONFIDENCE_BOOST}
            title="Confidence Boost"
            icon={<Plus className="h-4 w-4 text-blue-500" />}
            description="Adjusts bullish, bearish, or neutral confidence by specified percentage"
            onBlockDrop={onBlockDrop}
            properties={{ boostType: 'bullish', percentage: 10 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="output" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-white/70" />
              Output Signals
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
            <BlockItem
              type={BlockType.TRIGGER}
              title="Signal"
              icon={<Target className="h-4 w-4" />}
              description="Bullish, Bearish, or Neutral signal"
              onBlockDrop={onBlockDrop}
              properties={{ signal: 'bullish', confidence: 75 }}
            />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="debugging" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-white/70" />
              Debugging
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
            <BlockItem
              type={BlockType.CONSOLE_LOG}
              title="Console Log"
              icon={<Terminal className="h-4 w-4" />}
              description="Debug logging for agent execution"
              onBlockDrop={onBlockDrop}
              properties={{ message: 'Debug message' }}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default BlockPalette;
