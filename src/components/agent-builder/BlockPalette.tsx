import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import {
  Bar<PERSON>hart2, TrendingUp, TrendingDown, DollarSign, Calculator, GitBranch, Target, Zap, Plus, Minus,
  BarChart3, Activity, LineChart, Crosshair, Terminal, Shield, Percent, Clock, Globe, Layers,
  Building, RotateCcw, PieChart, ArrowUpDown, Newspaper, Filter, CheckCircle, Calendar,
  Volume2, Gauge, Radar, TrendingUpDown, Shuffle, AlertTriangle, Timer, Settings
} from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';

interface BlockPaletteProps {
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
}

interface BlockItemProps {
  type: BlockType;
  title: string;
  icon: React.ReactNode;
  description: string;
  onBlockDrop: (type: string, position: { x: number; y: number }) => void;
  properties?: Record<string, any>;
}

const BlockItem: React.FC<BlockItemProps> = ({ type, title, icon, description, onBlockDrop, properties }) => {
  const handleDragStart = (e: React.DragEvent) => {
    // Store both the type and any additional properties
    const dragData = {
      type,
      properties: properties || {}
    };

    // Ensure position is not included in the properties to avoid issues
    if (dragData.properties.position) {
      delete dragData.properties.position;
    }

    e.dataTransfer.setData('application/reactflow', JSON.stringify(dragData));
    e.dataTransfer.effectAllowed = 'move';
  };

  // Neutral detailed styling for all blocks
  const style = {
    gradient: 'from-white/[0.08] to-white/[0.02]',
    border: 'border-white/[0.15]',
    iconBg: 'bg-white/[0.08]',
    iconColor: 'text-white/90',
    glow: 'hover:shadow-[0_0_25px_rgba(255,255,255,0.12)]'
  };

  return (
    <div
      className="mb-1.5 cursor-grab rounded-lg transition-transform duration-200 hover:scale-[1.01] active:scale-95"
      draggable
      onDragStart={handleDragStart}
      onDragEnd={() => {
        // Subtle animation when dragged to canvas
        const element = document.querySelector(`[data-block-type="${type}"]`);
        if (element) {
          element.classList.add('animate-pulse');
          setTimeout(() => element.classList.remove('animate-pulse'), 300);
        }
      }}
    >
      <div className="p-2.5 flex items-center gap-2.5 border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent hover:border-white/[0.12] transition-colors duration-200">
        <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center rounded-md bg-white/[0.08] text-white/80 border border-white/[0.08]">
          <div className="w-3.5 h-3.5 flex items-center justify-center">
            {React.cloneElement(icon as React.ReactElement, {
              className: "w-3.5 h-3.5"
            })}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="text-xs font-medium text-white leading-tight">{title}</h3>
          <p className="text-[10px] text-white/60 leading-tight mt-0.5">{description}</p>
        </div>
      </div>
    </div>
  );
};

const BlockPalette: React.FC<BlockPaletteProps> = ({ onBlockDrop }) => {
  return (
    <div className="space-y-4">
      <Accordion type="multiple" defaultValue={['data', 'technical-indicators', 'market-analysis', 'risk-management', 'signal-generation', 'execution-timing', 'logic-flow', 'output', 'debugging']} className="space-y-3">
        <AccordionItem value="data" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <DollarSign className="w-4 h-4 text-white/70" />
              Data Sources
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.PRICE}
            title="Price Data"
            icon={<DollarSign className="h-4 w-4" />}
            description="Open, High, Low, Close, Volume"
            onBlockDrop={onBlockDrop}
            properties={{ dataPoint: 'close', lookback: 0 }}
          />
          <BlockItem
            type={BlockType.FUNDAMENTAL}
            title="Fundamental Data"
            icon={<TrendingUp className="h-4 w-4" />}
            description="P/E, P/B, ROE, Revenue Growth, etc."
            onBlockDrop={onBlockDrop}
            properties={{ metric: 'return_on_equity', statement: 'calculated', period: 'quarterly' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="technical-indicators" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <BarChart2 className="w-4 h-4 text-white/70" />
              Technical Indicators
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.MOVING_AVERAGE}
            title="Moving Averages"
            icon={<LineChart className="h-4 w-4" />}
            description="SMA, EMA, WMA, VWAP"
            onBlockDrop={onBlockDrop}
            properties={{ averageType: 'sma', period: 20, timeframe: 'day', source: 'close' }}
          />
          <BlockItem
            type={BlockType.MOMENTUM_INDICATOR}
            title="Momentum Indicators"
            icon={<Activity className="h-4 w-4" />}
            description="RSI, Stochastic, Williams %R, CCI"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'rsi', period: 14, timeframe: 'day', overbought: 70, oversold: 30 }}
          />
          <BlockItem
            type={BlockType.TREND_INDICATOR}
            title="Trend Indicators"
            icon={<TrendingUp className="h-4 w-4" />}
            description="MACD, ADX, Parabolic SAR, Ichimoku"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'macd', parameters: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 }, timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.VOLUME_INDICATOR}
            title="Volume Indicators"
            icon={<Volume2 className="h-4 w-4" />}
            description="OBV, Volume Profile, A/D Line"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'obv', parameters: { period: 20 }, timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.VOLATILITY_INDICATOR}
            title="Volatility Indicators"
            icon={<Gauge className="h-4 w-4" />}
            description="Bollinger Bands, ATR, Keltner Channels"
            onBlockDrop={onBlockDrop}
            properties={{ indicator: 'bollinger_bands', parameters: { period: 20, stdDev: 2 }, timeframe: 'day' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="market-analysis" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Radar className="w-4 h-4 text-white/70" />
              Market Analysis
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.SUPPORT_RESISTANCE}
            title="Support/Resistance"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Pivot points, swing levels, Fibonacci retracements"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'pivot_points', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.TREND_LINE_ANALYSIS}
            title="Trend Line Analysis"
            icon={<TrendingUp className="h-4 w-4" />}
            description="Automatic trend lines, channels, breakout lines"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'automatic_trendlines', minTouches: 2, lookbackPeriod: 50 }}
          />
          <BlockItem
            type={BlockType.CHART_PATTERN}
            title="Chart Pattern Recognition"
            icon={<Layers className="h-4 w-4" />}
            description="Head & shoulders, triangles, flags, wedges"
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.CANDLE_PATTERN}
            title="Candlestick Patterns"
            icon={<BarChart3 className="h-4 w-4" />}
            description="Doji, hammer, engulfing, morning/evening star"
            onBlockDrop={onBlockDrop}
            properties={{ pattern: 'any', timeframe: 'day' }}
          />
          <BlockItem
            type={BlockType.MARKET_STRUCTURE}
            title="Market Structure"
            icon={<Building className="h-4 w-4" />}
            description="Higher highs/lower lows, structure breaks"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'higher_highs_lows', lookbackPeriod: 20 }}
          />
          <BlockItem
            type={BlockType.BREAKOUT_DETECTION}
            title="Breakout Detection"
            icon={<ArrowUpDown className="h-4 w-4" />}
            description="Support/resistance breakouts, volume confirmation"
            onBlockDrop={onBlockDrop}
            properties={{ breakoutType: 'support_resistance', timeframe: 'day', volumeConfirmation: true }}
          />
          <BlockItem
            type={BlockType.GAP_ANALYSIS}
            title="Gap Analysis"
            icon={<Crosshair className="h-4 w-4" />}
            description="Gap up/down detection with size thresholds"
            onBlockDrop={onBlockDrop}
            properties={{ gapType: 'any', minGapSize: 1, timeframe: 'day' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="risk-management" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4 text-white/70" />
              Risk Management
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.POSITION_SIZING}
            title="Position Sizing"
            icon={<Percent className="h-4 w-4" />}
            description="Fixed, percentage, Kelly criterion, ATR-based"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'percentage', percentage: 2 }}
          />
          <BlockItem
            type={BlockType.STOP_LOSS}
            title="Stop Loss"
            icon={<Shield className="h-4 w-4" />}
            description="Fixed, trailing, ATR-based, structure-based"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'fixed_percentage', percentage: 2 }}
          />
          <BlockItem
            type={BlockType.TAKE_PROFIT}
            title="Take Profit"
            icon={<Target className="h-4 w-4" />}
            description="Fixed, risk-reward ratio, Fibonacci levels"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'risk_reward_ratio', ratio: 2 }}
          />
          <BlockItem
            type={BlockType.PORTFOLIO_RISK}
            title="Portfolio Risk Controls"
            icon={<AlertTriangle className="h-4 w-4" />}
            description="Max drawdown, correlation limits, sector exposure"
            onBlockDrop={onBlockDrop}
            properties={{ method: 'max_drawdown', maxDrawdown: 10 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="signal-generation" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Zap className="w-4 h-4 text-white/70" />
              Signal Generation
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.PRICE_ACTION_SIGNAL}
            title="Price Action Signals"
            icon={<TrendingUpDown className="h-4 w-4" />}
            description="Breakouts, reversals, pullbacks, momentum shifts"
            onBlockDrop={onBlockDrop}
            properties={{ signalType: 'breakout', lookbackPeriod: 20, volumeConfirmation: true }}
          />
          <BlockItem
            type={BlockType.MULTI_TIMEFRAME_ANALYSIS}
            title="Multi-Timeframe Analysis"
            icon={<Layers className="h-4 w-4" />}
            description="Trend alignment, higher TF bias, confluence"
            onBlockDrop={onBlockDrop}
            properties={{ signalType: 'trend_alignment', primaryTimeframe: 'day', secondaryTimeframe: 'hour' }}
          />
          <BlockItem
            type={BlockType.DIVERGENCE_DETECTION}
            title="Divergence Detection"
            icon={<Shuffle className="h-4 w-4" />}
            description="RSI, MACD, volume divergences"
            onBlockDrop={onBlockDrop}
            properties={{ signalType: 'rsi_divergence', rsiPeriod: 14, lookbackPeriod: 20 }}
          />
          <BlockItem
            type={BlockType.VOLUME_CONFIRMATION}
            title="Volume Confirmation"
            icon={<Volume2 className="h-4 w-4" />}
            description="Volume spikes, trends, accumulation/distribution"
            onBlockDrop={onBlockDrop}
            properties={{ signalType: 'volume_spike', lookbackPeriod: 20, spikeMultiplier: 2 }}
          />
          <BlockItem
            type={BlockType.MARKET_REGIME}
            title="Market Regime Detection"
            icon={<Gauge className="h-4 w-4" />}
            description="Trending vs ranging, volatility regime, sentiment"
            onBlockDrop={onBlockDrop}
            properties={{ signalType: 'trending_vs_ranging', adxPeriod: 14, trendThreshold: 25 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="execution-timing" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4 text-white/70" />
              Execution & Timing
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.ENTRY_TIMING}
            title="Entry Timing"
            icon={<Timer className="h-4 w-4" />}
            description="Market open, specific times, event-based, volatility-based"
            onBlockDrop={onBlockDrop}
            properties={{ timingType: 'market_open', offsetMinutes: 0, timezone: 'US/Eastern' }}
          />
          <BlockItem
            type={BlockType.EXIT_CONDITIONS}
            title="Exit Conditions"
            icon={<Target className="h-4 w-4" />}
            description="Time-based, profit targets, stop loss, market close"
            onBlockDrop={onBlockDrop}
            properties={{ timingType: 'time_based', maxHoldingPeriod: 24, timeUnit: 'hours' }}
          />
          <BlockItem
            type={BlockType.SESSION_FILTER}
            title="Market Session Filter"
            icon={<Globe className="h-4 w-4" />}
            description="Regular hours, pre-market, after hours, custom sessions"
            onBlockDrop={onBlockDrop}
            properties={{ timingType: 'regular_hours', startTime: '09:30', endTime: '16:00', timezone: 'US/Eastern' }}
          />
          <BlockItem
            type={BlockType.ECONOMIC_CALENDAR}
            title="Economic Calendar"
            icon={<Calendar className="h-4 w-4" />}
            description="Earnings filter, Fed events, economic data, dividend dates"
            onBlockDrop={onBlockDrop}
            properties={{ timingType: 'earnings_filter', beforeAfter: 'exclude_both', offsetHours: 2 }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="logic-flow" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <GitBranch className="w-4 h-4 text-white/70" />
              Logic & Flow Control
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
          <BlockItem
            type={BlockType.IF_THEN_ELSE}
            title="If-Then-Else"
            icon={<GitBranch className="h-4 w-4" />}
            description="Conditional logic with true/false branches"
            onBlockDrop={onBlockDrop}
            properties={{ operator: 'greater_than', threshold: 50 }}
          />
          <BlockItem
            type={BlockType.AND_OPERATOR}
            title="AND Operator"
            icon={<Plus className="h-4 w-4" />}
            description="Logical AND - all inputs must be true"
            onBlockDrop={onBlockDrop}
            properties={{ requireAll: true }}
          />
          <BlockItem
            type={BlockType.OR_OPERATOR}
            title="OR Operator"
            icon={<Plus className="h-4 w-4" />}
            description="Logical OR - any input can be true"
            onBlockDrop={onBlockDrop}
            properties={{ requireAny: true }}
          />
          <BlockItem
            type={BlockType.NOT_OPERATOR}
            title="NOT Operator"
            icon={<Minus className="h-4 w-4" />}
            description="Logical NOT - inverts the input signal"
            onBlockDrop={onBlockDrop}
            properties={{ invert: true }}
          />
          <BlockItem
            type={BlockType.SIGNAL_CONFIRMATION}
            title="Signal Confirmation"
            icon={<CheckCircle className="h-4 w-4" />}
            description="Requires multiple signals to agree before confirming"
            onBlockDrop={onBlockDrop}
            properties={{ confirmationType: 'both_agree', confidenceThreshold: 70, timeWindow: 5 }}
          />
          <BlockItem
            type={BlockType.TIME_FILTER}
            title="Time Filter"
            icon={<Filter className="h-4 w-4" />}
            description="Filters signals based on time conditions"
            onBlockDrop={onBlockDrop}
            properties={{ filterType: 'time_range', startTime: '09:30', endTime: '16:00', timezone: 'US/Eastern' }}
          />
          <BlockItem
            type={BlockType.MARKET_CONDITION_FILTER}
            title="Market Condition Filter"
            icon={<Filter className="h-4 w-4" />}
            description="Filters based on market conditions (volatility, volume, etc.)"
            onBlockDrop={onBlockDrop}
            properties={{ conditionType: 'volatility', threshold: 20, operator: 'less_than' }}
          />
          <BlockItem
            type={BlockType.CONDITION}
            title="Basic Condition"
            icon={<GitBranch className="h-4 w-4" />}
            description="Simple comparison operators (>, <, ==, etc.)"
            onBlockDrop={onBlockDrop}
            properties={{ operator: '>', compareValue: 0 }}
          />
          <BlockItem
            type={BlockType.OPERATOR}
            title="Math Operator"
            icon={<Calculator className="h-4 w-4" />}
            description="Mathematical operations (add, subtract, multiply, etc.)"
            onBlockDrop={onBlockDrop}
            properties={{ operation: 'add' }}
          />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="output" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Target className="w-4 h-4 text-white/70" />
              Output Signals
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
            <BlockItem
              type={BlockType.TRIGGER}
              title="Signal Output"
              icon={<Target className="h-4 w-4" />}
              description="Final bullish, bearish, or neutral signal"
              onBlockDrop={onBlockDrop}
              properties={{ signal: 'bullish', confidence: 75 }}
            />
            <BlockItem
              type={BlockType.CONFIDENCE_BOOST}
              title="Confidence Boost"
              icon={<Plus className="h-4 w-4" />}
              description="Adjusts signal confidence by specified percentage"
              onBlockDrop={onBlockDrop}
              properties={{ boostType: 'bullish', percentage: 10 }}
            />
            <BlockItem
              type={BlockType.STOCK_SENTIMENT}
              title="Stock Sentiment"
              icon={<Newspaper className="h-4 w-4" />}
              description="News sentiment analysis for additional signal confirmation"
              onBlockDrop={onBlockDrop}
              properties={{ articleLimit: 10, daysBack: 7 }}
            />
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="debugging" className="border border-white/[0.08] rounded-lg bg-gradient-to-br from-white/[0.03] to-transparent">
          <AccordionTrigger className="py-3 px-4 text-sm font-medium text-white hover:text-white/90 transition-colors">
            <div className="flex items-center gap-2">
              <Terminal className="w-4 h-4 text-white/70" />
              Debugging & Utilities
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 space-y-1">
            <BlockItem
              type={BlockType.CONSOLE_LOG}
              title="Console Log"
              icon={<Terminal className="h-4 w-4" />}
              description="Debug logging for agent execution and troubleshooting"
              onBlockDrop={onBlockDrop}
              properties={{ message: 'Debug message' }}
            />
            <BlockItem
              type={BlockType.INDICATOR}
              title="Legacy Technical Indicator"
              icon={<Settings className="h-4 w-4" />}
              description="Legacy indicator block for backward compatibility"
              onBlockDrop={onBlockDrop}
              properties={{ indicator: 'rsi', parameters: { period: 14 } }}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default BlockPalette;
