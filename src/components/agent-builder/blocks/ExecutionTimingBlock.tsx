import React from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Handle, Position } from 'reactflow';
import { Clock, X, Calendar, Globe, Target } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface ExecutionTimingBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const ExecutionTimingBlock: React.FC<ExecutionTimingBlockProps> = ({ data, selected }) => {
  
  const getBlockConfig = (type: string) => {
    switch (type) {
      case 'ENTRY_TIMING':
        return {
          title: 'Entry Timing',
          icon: Clock,
          color: 'green',
          timings: [
            { value: 'market_open', label: 'Market Open', params: { offsetMinutes: 0, timezone: 'US/Eastern' } },
            { value: 'specific_time', label: 'Specific Time', params: { hour: 9, minute: 30, timezone: 'US/Eastern' } },
            { value: 'event_based', label: 'Event-Based', params: { eventType: 'earnings', beforeAfter: 'after', offsetHours: 1 } },
            { value: 'volatility_based', label: 'Volatility-Based', params: { atrPeriod: 14, volatilityThreshold: 1.5 } },
            { value: 'volume_based', label: 'Volume-Based', params: { volumePeriod: 20, volumeThreshold: 1.5 } }
          ]
        };
      case 'EXIT_CONDITIONS':
        return {
          title: 'Exit Conditions',
          icon: Target,
          color: 'red',
          timings: [
            { value: 'time_based', label: 'Time-Based', params: { maxHoldingPeriod: 24, timeUnit: 'hours' } },
            { value: 'profit_target', label: 'Profit Target', params: { targetPercentage: 5, partialExit: false } },
            { value: 'stop_loss', label: 'Stop Loss', params: { stopPercentage: 2, trailingStop: false } },
            { value: 'market_close', label: 'Market Close', params: { offsetMinutes: -30, forceExit: true } },
            { value: 'signal_reversal', label: 'Signal Reversal', params: { confirmationPeriod: 2, exitOnOpposite: true } }
          ]
        };
      case 'SESSION_FILTER':
        return {
          title: 'Session Filter',
          icon: Globe,
          color: 'blue',
          timings: [
            { value: 'regular_hours', label: 'Regular Hours', params: { startTime: '09:30', endTime: '16:00', timezone: 'US/Eastern' } },
            { value: 'pre_market', label: 'Pre-Market', params: { startTime: '04:00', endTime: '09:30', timezone: 'US/Eastern' } },
            { value: 'after_hours', label: 'After Hours', params: { startTime: '16:00', endTime: '20:00', timezone: 'US/Eastern' } },
            { value: 'custom_session', label: 'Custom Session', params: { startTime: '10:00', endTime: '15:00', timezone: 'US/Eastern' } },
            { value: 'exclude_session', label: 'Exclude Session', params: { excludeStart: '11:30', excludeEnd: '13:30', timezone: 'US/Eastern' } }
          ]
        };
      case 'ECONOMIC_CALENDAR':
        return {
          title: 'Economic Calendar',
          icon: Calendar,
          color: 'purple',
          timings: [
            { value: 'earnings_filter', label: 'Earnings Filter', params: { beforeAfter: 'exclude_both', offsetHours: 2 } },
            { value: 'fed_events', label: 'Fed Events', params: { eventTypes: ['fomc', 'speech'], beforeAfter: 'exclude_both', offsetHours: 4 } },
            { value: 'economic_data', label: 'Economic Data', params: { importance: 'high', beforeAfter: 'exclude_both', offsetMinutes: 30 } },
            { value: 'dividend_ex_date', label: 'Dividend Ex-Date', params: { beforeAfter: 'exclude_both', offsetDays: 1 } }
          ]
        };
      default:
        return {
          title: 'Execution Timing',
          icon: Clock,
          color: 'blue',
          timings: [
            { value: 'immediate', label: 'Immediate', params: {} }
          ]
        };
    }
  };

  const config = getBlockConfig(data.type);
  const IconComponent = config.icon;
  const currentTiming = config.timings.find(timing => timing.value === data.parameters.timingType) || config.timings[0];

  const handleTimingChange = (timingType: string) => {
    const selectedTiming = config.timings.find(t => t.value === timingType);
    if (selectedTiming) {
      data.onUpdate({
        parameters: {
          ...data.parameters,
          timingType,
          ...selectedTiming.params
        }
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...data.parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentTiming) return null;

    return Object.entries(currentTiming.params).map(([param, defaultValue]) => {
      const currentValue = data.parameters[param] ?? defaultValue;
      
      // Special handling for different parameter types
      if (param === 'timezone') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Timezone</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="US/Eastern">US/Eastern</SelectItem>
                <SelectItem value="US/Central">US/Central</SelectItem>
                <SelectItem value="US/Mountain">US/Mountain</SelectItem>
                <SelectItem value="US/Pacific">US/Pacific</SelectItem>
                <SelectItem value="UTC">UTC</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'beforeAfter') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Before/After</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="before">Before</SelectItem>
                <SelectItem value="after">After</SelectItem>
                <SelectItem value="exclude_both">Exclude Both</SelectItem>
                <SelectItem value="include_both">Include Both</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'timeUnit') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Time Unit</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="minutes">Minutes</SelectItem>
                <SelectItem value="hours">Hours</SelectItem>
                <SelectItem value="days">Days</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'importance') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Importance</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (typeof defaultValue === 'boolean') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Select value={currentValue.toString()} onValueChange={value => handleParameterChange(param, value === 'true')}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (Array.isArray(defaultValue)) {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Input
              type="text"
              value={Array.isArray(currentValue) ? currentValue.join(', ') : currentValue}
              onChange={e => handleParameterChange(param, e.target.value.split(', '))}
              className="h-8 text-xs"
              placeholder="Comma separated values"
            />
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type={param.includes('Time') ? 'time' : 'number'}
            value={currentValue}
            onChange={e => handleParameterChange(param, e.target.value)}
            className="h-8 text-xs"
            min={0}
            step={param.includes('percentage') || param.includes('threshold') ? 0.1 : 1}
          />
        </div>
      );
    });
  };

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      green: { bg: 'bg-green-500/10', text: 'text-green-500' },
      red: { bg: 'bg-red-500/10', text: 'text-red-500' },
      blue: { bg: 'bg-blue-500/10', text: 'text-blue-500' },
      purple: { bg: 'bg-purple-500/10', text: 'text-purple-500' }
    };
    return colorMap[color] || colorMap.blue;
  };

  const colorClasses = getColorClasses(config.color);

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Timing Type Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Timing Type</label>
            <Select value={data.parameters.timingType || currentTiming.value} onValueChange={handleTimingChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.timings.map(timing => (
                  <SelectItem key={timing.value} value={timing.value}>
                    {timing.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handles */}
      <Handle
        type="source"
        position={Position.Right}
        id="allow"
        style={{ top: '40%' }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="block"
        style={{ top: '60%' }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </>
  );
};

export default ExecutionTimingBlock;
