import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import {
  Activity, LineChart, Volume2, Gauge, Shield, Clock, Calendar, Globe, Users,
  Layers, Scale, Percent, TrendingUpDown, AlertTriangle, Timer, Building,
  Crosshair, ArrowUpDown, RotateCcw, PieChart, BarChart2, TrendingUp, Trash2, Terminal, Newspaper
} from 'lucide-react';
import { BlockType } from '@/hooks/useAgentBuilder';
import { AgentBlock } from '@/services/agentService';

interface GenericBlockProps {
  data: {
    id: string;
    type: string;
    timeframe?: string;
    period?: number;
    indicator?: string;
    method?: string;
    threshold?: number;
    percentage?: number;
    isEntryBlock?: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
    [key: string]: any; // Allow additional properties
  };
  selected: boolean;
}

const GenericBlock: React.FC<GenericBlockProps> = ({ data, selected }) => {
  // Check if this block is connected to other blocks
  const isConnected = (data.inputConnections && data.inputConnections.length > 0) ||
                     (data.outputConnections && data.outputConnections.length > 0) ||
                     data.trueConnection || data.falseConnection || data.neutralConnection;

  const getBlockInfo = (type: string) => {
    switch (type) {
      case BlockType.TREND_INDICATOR:
        return { title: 'Trend Indicator', icon: TrendingUpDown, description: 'MACD, ADX, Parabolic SAR, Ichimoku' };
      case BlockType.VOLUME_INDICATOR:
        return { title: 'Volume Indicator', icon: Volume2, description: 'Volume Profile, OBV, VWAP, MFI' };
      case BlockType.VOLATILITY_INDICATOR:
        return { title: 'Volatility Indicator', icon: Gauge, description: 'Bollinger Bands, ATR, Keltner Channels' };
      case BlockType.SUPPORT_RESISTANCE:
        return { title: 'Support/Resistance', icon: BarChart2, description: 'Pivot points, Fibonacci levels' };
      case BlockType.CHART_PATTERN:
        return { title: 'Chart Pattern', icon: TrendingUp, description: 'Triangles, flags, head & shoulders' };
      case BlockType.BREAKOUT_DETECTION:
        return { title: 'Breakout Detection', icon: ArrowUpDown, description: 'Price breaking key levels' };
      case BlockType.GAP_ANALYSIS:
        return { title: 'Gap Analysis', icon: Crosshair, description: 'Gap up/down detection' };
      case BlockType.STOP_LOSS:
        return { title: 'Stop Loss', icon: Shield, description: 'Risk management stop loss' };
      case BlockType.TAKE_PROFIT:
        return { title: 'Take Profit', icon: Percent, description: 'Profit taking targets' };
      case BlockType.RISK_LIMIT:
        return { title: 'Risk Limits', icon: AlertTriangle, description: 'Daily loss and exposure limits' };
      case BlockType.TIME_FILTER:
        return { title: 'Time Filter', icon: Clock, description: 'Time-based trading filters' };
      case BlockType.SESSION_FILTER:
        return { title: 'Session Filter', icon: Timer, description: 'Market session filters' };
      case BlockType.ECONOMIC_CALENDAR:
        return { title: 'Economic Calendar', icon: Calendar, description: 'Economic events filter' };
      case BlockType.TREND_DETECTION:
        return { title: 'Trend Detection', icon: TrendingUpDown, description: 'Market trend analysis' };
      case BlockType.VOLATILITY_REGIME:
        return { title: 'Volatility Regime', icon: Gauge, description: 'Volatility environment detection' };
      case BlockType.MARKET_SENTIMENT:
        return { title: 'Market Sentiment', icon: Users, description: 'VIX, put/call ratio analysis' };
      case BlockType.MARKET_BREADTH:
        return { title: 'Market Breadth', icon: Globe, description: 'Advance/decline indicators' };
      case BlockType.MULTI_TIMEFRAME:
        return { title: 'Multi-Timeframe', icon: Layers, description: 'Multi-timeframe confirmation' };
      case BlockType.CONFLUENCE:
        return { title: 'Confluence', icon: Building, description: 'Multiple signal alignment' };
      case BlockType.SCALE_STRATEGY:
        return { title: 'Scale Strategy', icon: RotateCcw, description: 'Scale-in/out strategies' };
      case BlockType.PARTIAL_PROFIT:
        return { title: 'Partial Profit', icon: PieChart, description: 'Partial profit taking' };
      case BlockType.CONSOLE_LOG:
        return { title: 'Console Log', icon: Terminal, description: 'Debug logging for agent execution' };
      case BlockType.STOCK_SENTIMENT:
        return { title: 'Stock Sentiment', icon: Newspaper, description: 'News sentiment analysis' };
      default:
        return { title: 'Trading Block', icon: Activity, description: 'Trading strategy component' };
    }
  };

  const blockInfo = getBlockInfo(data.type);
  const IconComponent = blockInfo.icon;

  // Determine if this block has input/output connections
  const hasInput = data.inputConnections !== undefined ||
                   ['RISK_LIMIT', 'MARKET_BREADTH', 'MULTI_TIMEFRAME', 'CONFLUENCE', 'SCALE_STRATEGY'].includes(data.type);

  const hasOutput = data.outputConnections !== undefined ||
                    !['PARTIAL_PROFIT'].includes(data.type);

  // Handle property updates
  const handleUpdate = (field: string, value: any) => {
    data.onUpdate({ [field]: value });
  };

  // Get timeframe options
  const timeframeOptions = [
    { value: '1min', label: '1 min' },
    { value: '5min', label: '5 min' },
    { value: '15min', label: '15 min' },
    { value: '1hour', label: '1 hour' },
    { value: '4hour', label: '4 hour' },
    { value: 'day', label: 'Daily' }
  ];

  // Render configuration based on block type
  const renderConfiguration = () => {
    // Always show configuration since settings are always open

    const commonTimeframe = (
      <div>
        <Label className="text-xs">Timeframe</Label>
        <Select value={data.timeframe || '1hour'} onValueChange={(value) => handleUpdate('timeframe', value)}>
          <SelectTrigger className="h-8">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {timeframeOptions.map(option => (
              <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );

    const commonPeriod = (
      <div>
        <Label className="text-xs">Period</Label>
        <Input
          type="number"
          value={data.period || 20}
          onChange={(e) => handleUpdate('period', parseInt(e.target.value) || 20)}
          className="h-8"
          min="1"
          max="200"
        />
      </div>
    );

    switch (data.type) {
      case BlockType.TREND_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'macd'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="macd">MACD</SelectItem>
                  <SelectItem value="adx">ADX</SelectItem>
                  <SelectItem value="parabolic_sar">Parabolic SAR</SelectItem>
                  <SelectItem value="ichimoku">Ichimoku</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {data.indicator === 'macd' && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Fast Period</Label>
                  <Input
                    type="number"
                    value={data.fastPeriod || 12}
                    onChange={(e) => handleUpdate('fastPeriod', parseInt(e.target.value) || 12)}
                    className="h-8"
                    min="1"
                  />
                </div>
                <div>
                  <Label className="text-xs">Slow Period</Label>
                  <Input
                    type="number"
                    value={data.slowPeriod || 26}
                    onChange={(e) => handleUpdate('slowPeriod', parseInt(e.target.value) || 26)}
                    className="h-8"
                    min="1"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case BlockType.VOLUME_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'vwap'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="vwap">VWAP</SelectItem>
                  <SelectItem value="obv">OBV</SelectItem>
                  <SelectItem value="volume_profile">Volume Profile</SelectItem>
                  <SelectItem value="mfi">Money Flow Index</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {data.indicator === 'mfi' && commonPeriod}
          </div>
        );

      case BlockType.VOLATILITY_INDICATOR:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Indicator</Label>
              <Select value={data.indicator || 'bollinger_bands'} onValueChange={(value) => handleUpdate('indicator', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="bollinger_bands">Bollinger Bands</SelectItem>
                  <SelectItem value="atr">ATR</SelectItem>
                  <SelectItem value="keltner_channels">Keltner Channels</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            {commonPeriod}
            {(data.indicator === 'bollinger_bands' || data.indicator === 'keltner_channels') && (
              <div>
                <Label className="text-xs">Multiplier</Label>
                <Input
                  type="number"
                  value={data.multiplier || 2}
                  onChange={(e) => handleUpdate('multiplier', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.1"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.TIME_FILTER:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Filter Type</Label>
              <Select value={data.filterType || 'time_of_day'} onValueChange={(value) => handleUpdate('filterType', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="time_of_day">Time of Day</SelectItem>
                  <SelectItem value="day_of_week">Day of Week</SelectItem>
                  <SelectItem value="market_open_close">Market Open/Close</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.filterType === 'time_of_day' && (
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label className="text-xs">Start Time</Label>
                  <Input
                    type="time"
                    value={data.startTime || '09:30'}
                    onChange={(e) => handleUpdate('startTime', e.target.value)}
                    className="h-8"
                  />
                </div>
                <div>
                  <Label className="text-xs">End Time</Label>
                  <Input
                    type="time"
                    value={data.endTime || '16:00'}
                    onChange={(e) => handleUpdate('endTime', e.target.value)}
                    className="h-8"
                  />
                </div>
              </div>
            )}
          </div>
        );

      case BlockType.STOP_LOSS:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Method</Label>
              <Select value={data.method || 'percentage'} onValueChange={(value) => handleUpdate('method', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="percentage">Percentage</SelectItem>
                  <SelectItem value="fixed_price">Fixed Price</SelectItem>
                  <SelectItem value="atr_based">ATR Based</SelectItem>
                  <SelectItem value="trailing">Trailing</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.method === 'percentage' && (
              <div>
                <Label className="text-xs">Percentage (%)</Label>
                <Input
                  type="number"
                  value={data.percentage || 2}
                  onChange={(e) => handleUpdate('percentage', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.1"
                  max="50"
                  step="0.1"
                />
              </div>
            )}
            {data.method === 'atr_based' && (
              <div>
                <Label className="text-xs">ATR Multiplier</Label>
                <Input
                  type="number"
                  value={data.atrMultiplier || 2}
                  onChange={(e) => handleUpdate('atrMultiplier', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.5"
                  max="10"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.TAKE_PROFIT:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Method</Label>
              <Select value={data.method || 'risk_reward_ratio'} onValueChange={(value) => handleUpdate('method', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="risk_reward_ratio">Risk/Reward Ratio</SelectItem>
                  <SelectItem value="fixed_price">Fixed Price</SelectItem>
                  <SelectItem value="multiple_targets">Multiple Targets</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {data.method === 'risk_reward_ratio' && (
              <div>
                <Label className="text-xs">Risk/Reward Ratio</Label>
                <Input
                  type="number"
                  value={data.riskRewardRatio || 2}
                  onChange={(e) => handleUpdate('riskRewardRatio', parseFloat(e.target.value) || 2)}
                  className="h-8"
                  min="0.5"
                  max="10"
                  step="0.1"
                />
              </div>
            )}
          </div>
        );

      case BlockType.BREAKOUT_DETECTION:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Level Type</Label>
              <Select value={data.level || 'resistance'} onValueChange={(value) => handleUpdate('level', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="resistance">Resistance</SelectItem>
                  <SelectItem value="range_high">Range High</SelectItem>
                  <SelectItem value="range_low">Range Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Volume Confirmation</Label>
              <Select value={data.volumeConfirmation ? 'true' : 'false'} onValueChange={(value) => handleUpdate('volumeConfirmation', value === 'true')}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="true">Required</SelectItem>
                  <SelectItem value="false">Not Required</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        );

      case BlockType.CHART_PATTERN:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Pattern Type</Label>
              <Select value={data.pattern || 'any'} onValueChange={(value) => handleUpdate('pattern', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any Pattern</SelectItem>
                  <SelectItem value="triangle">Triangle</SelectItem>
                  <SelectItem value="flag">Flag</SelectItem>
                  <SelectItem value="pennant">Pennant</SelectItem>
                  <SelectItem value="head_and_shoulders">Head and Shoulders</SelectItem>
                  <SelectItem value="double_top">Double Top</SelectItem>
                  <SelectItem value="double_bottom">Double Bottom</SelectItem>
                  <SelectItem value="cup_and_handle">Cup and Handle</SelectItem>
                  <SelectItem value="wedge">Wedge</SelectItem>
                  <SelectItem value="channel">Channel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 50}
                onChange={(e) => handleUpdate('lookbackPeriod', parseInt(e.target.value) || 50)}
                className="h-8"
                min="10"
                max="200"
              />
            </div>
            <div>
              <Label className="text-xs">Min Pattern Size</Label>
              <Input
                type="number"
                value={data.minPatternSize || 10}
                onChange={(e) => handleUpdate('minPatternSize', parseInt(e.target.value) || 10)}
                className="h-8"
                min="5"
                max="50"
              />
            </div>
          </div>
        );

      case BlockType.SUPPORT_RESISTANCE:
        return (
          <div className="space-y-2">
            <div>
              <Label className="text-xs">Output Type</Label>
              <Select value={data.outputType || 'support'} onValueChange={(value) => handleUpdate('outputType', value)}>
                <SelectTrigger className="h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="support">Support Level</SelectItem>
                  <SelectItem value="resistance">Resistance Level</SelectItem>
                </SelectContent>
              </Select>
            </div>
            {commonTimeframe}
            <div>
              <Label className="text-xs">Lookback Period</Label>
              <Input
                type="number"
                value={data.lookbackPeriod || 20}
                onChange={(e) => handleUpdate('lookbackPeriod', parseInt(e.target.value) || 20)}
                className="h-8"
                min="5"
                max="100"
              />
            </div>
            <div>
              <Label className="text-xs">Strength (Min Touches)</Label>
              <Input
                type="number"
                value={data.strength || 2}
                onChange={(e) => handleUpdate('strength', parseInt(e.target.value) || 2)}
                className="h-8"
                min="1"
                max="10"
              />
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-2">
            {data.timeframe !== undefined && commonTimeframe}
            {data.period !== undefined && commonPeriod}
            {data.threshold !== undefined && (
              <div>
                <Label className="text-xs">Threshold</Label>
                <Input
                  type="number"
                  value={data.threshold || 0}
                  onChange={(e) => handleUpdate('threshold', parseFloat(e.target.value) || 0)}
                  className="h-8"
                  step="0.1"
                />
              </div>
            )}
            {data.method !== undefined && (
              <div>
                <Label className="text-xs">Method</Label>
                <Input
                  value={data.method || ''}
                  onChange={(e) => handleUpdate('method', e.target.value)}
                  className="h-8"
                  placeholder="Enter method"
                />
              </div>
            )}
          </div>
        );
    }
  };

  return (
    <div className="relative">
      {/* Input handle */}
      {hasInput && (
        <Handle
          type="target"
          position={Position.Top}
          id="input"
          className={`transition-all duration-200 ${(data.inputConnections && data.inputConnections.length > 0) ? 'connected' : ''} ${data.isConnecting ? 'connecting-mode' : ''}`}
          style={{
            background: (data.inputConnections && data.inputConnections.length > 0) ? '#10b981' : 'rgba(255, 255, 255, 0.8)',
            width: '12px',
            height: '12px',
            border: (data.inputConnections && data.inputConnections.length > 0) ? '2px solid rgba(16, 185, 129, 0.5)' : '2px solid rgba(255, 255, 255, 0.3)',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
            zIndex: data.isConnecting ? 30 : 10
          }}
        />
      )}

      {/* Output handle(s) - improved positioning for multiple outputs */}
      {hasOutput && (
        <>
          {/* Check if this block has multiple output types (condition, pattern, etc.) */}
          {(data.type === 'CONDITION' || data.type === 'CANDLE_PATTERN' || data.type === 'CHART_PATTERN' || data.type === 'STOCK_SENTIMENT') ? (
            <>
              {/* Multiple output handles for conditional blocks */}
              {data.type === 'CONDITION' && (
                <>
                  <Handle
                    type="source"
                    position={Position.Right}
                    id="true"
                    className="transition-all duration-200"
                    style={{
                      background: data.trueConnection ? '#22c55e' : 'rgba(34, 197, 94, 0.8)',
                      width: '12px',
                      height: '12px',
                      border: '2px solid rgba(34, 197, 94, 0.5)',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      top: '35%'
                    }}
                  />
                  <Handle
                    type="source"
                    position={Position.Right}
                    id="false"
                    className="transition-all duration-200"
                    style={{
                      background: data.falseConnection ? '#ef4444' : 'rgba(239, 68, 68, 0.8)',
                      width: '12px',
                      height: '12px',
                      border: '2px solid rgba(239, 68, 68, 0.5)',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      top: '65%'
                    }}
                  />
                </>
              )}
              {(data.type === 'CANDLE_PATTERN' || data.type === 'CHART_PATTERN' || data.type === 'STOCK_SENTIMENT') && (
                <>
                  <Handle
                    type="source"
                    position={Position.Right}
                    id="bullish"
                    className="transition-all duration-200"
                    style={{
                      background: data.bullishConnection ? '#22c55e' : 'rgba(34, 197, 94, 0.8)',
                      width: '12px',
                      height: '12px',
                      border: '2px solid rgba(34, 197, 94, 0.5)',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      top: '25%'
                    }}
                  />
                  <Handle
                    type="source"
                    position={Position.Right}
                    id="bearish"
                    className="transition-all duration-200"
                    style={{
                      background: data.bearishConnection ? '#ef4444' : 'rgba(239, 68, 68, 0.8)',
                      width: '12px',
                      height: '12px',
                      border: '2px solid rgba(239, 68, 68, 0.5)',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      top: '50%'
                    }}
                  />
                  <Handle
                    type="source"
                    position={Position.Right}
                    id="neutral"
                    className="transition-all duration-200"
                    style={{
                      background: data.neutralConnection ? '#6b7280' : 'rgba(107, 114, 128, 0.8)',
                      width: '12px',
                      height: '12px',
                      border: '2px solid rgba(107, 114, 128, 0.5)',
                      boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)',
                      top: '75%'
                    }}
                  />
                </>
              )}
            </>
          ) : (
            /* Single output handle for standard blocks */
            <Handle
              type="source"
              position={Position.Bottom}
              id="output"
              className={`transition-all duration-200 ${(data.outputConnections && data.outputConnections.length > 0) ? 'connected' : ''}`}
              style={{
                background: (data.outputConnections && data.outputConnections.length > 0) ? '#10b981' : 'rgba(255, 255, 255, 0.8)',
                width: '12px',
                height: '12px',
                border: (data.outputConnections && data.outputConnections.length > 0) ? '2px solid rgba(16, 185, 129, 0.5)' : '2px solid rgba(255, 255, 255, 0.3)',
                boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
              }}
            />
          )}
        </>
      )}

      <div className={`
        relative w-40 overflow-hidden rounded-lg border border-white/[0.12]
        bg-gradient-to-br from-white/[0.05] to-white/[0.02]
        backdrop-blur-sm
        shadow-[inset_0_1px_0_rgba(255,255,255,0.08),0_2px_6px_rgba(0,0,0,0.2)]
        hover:shadow-[inset_0_1px_0_rgba(255,255,255,0.12),0_4px_12px_rgba(0,0,0,0.25)]
        hover:border-white/[0.18]
        transition-all duration-200 ease-out
        ${selected ? 'ring-1 ring-white/20 shadow-[0_0_8px_rgba(255,255,255,0.06),inset_0_0_0_1px_rgba(255,255,255,0.08)]' : ''}
        ${data.isEntryBlock ? 'border-green-400/40 shadow-[0_0_10px_rgba(34,197,94,0.15)]' : ''}
        ${data.hasError ? 'border-red-400/40 shadow-[0_0_10px_rgba(239,68,68,0.15)]' : ''}
      `}>
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/[0.03] via-transparent to-black/[0.01] pointer-events-none" />

        {/* Header with icon and title - compact vertical layout */}
        <div className="relative p-3 pb-2 flex flex-col items-center text-center">
          <div className="
            relative w-7 h-7 flex items-center justify-center rounded-lg mb-2
            bg-white/[0.08] text-white/90
            border border-white/[0.08]
            shadow-[inset_0_1px_0_rgba(255,255,255,0.15)]
          ">
            <IconComponent className="relative h-4 w-4" />
          </div>
          <h3 className="text-xs font-medium text-white leading-tight mb-1.5">{blockInfo.title}</h3>

          {/* Connected status */}
          {isConnected && (
            <div className="mb-1.5">
              <div className="px-2 py-0.5 bg-green-500 text-white text-[10px] font-medium rounded-md flex items-center gap-1 justify-center">
                <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                Connected
              </div>
            </div>
          )}

          {/* Action buttons */}
          <div className="flex gap-1 justify-center">
            <button
              className="
                w-5 h-5 flex items-center justify-center rounded-md
                bg-white/[0.05] hover:bg-red-500/20
                border border-white/[0.08] hover:border-red-400/30
                text-white/60 hover:text-red-300
                shadow-[inset_0_1px_0_rgba(255,255,255,0.1)]
                transition-all duration-200
              "
              onClick={(e) => {
                e.stopPropagation();
                console.log('Delete button clicked for block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-2.5 w-2.5" />
            </button>
          </div>
        </div>

        {/* Content area */}
        <div className="relative px-3 pb-3">
          <div className="space-y-2">
            <div className="text-[10px] text-white/65 leading-tight text-center">
              {blockInfo.description}
            </div>

            {/* Configuration interface - always shown */}
            <div className="space-y-1.5">
              {renderConfiguration()}
            </div>

            {/* Error messages */}
            {data.hasError && data.errorMessages && data.errorMessages.length > 0 && (
              <div className="mt-2 p-2 bg-red-500/10 border border-red-400/20 rounded-md">
                <div className="flex items-center gap-1 text-red-300 font-medium text-[10px] mb-1">
                  <span>⚠️ Errors:</span>
                </div>
                <ul className="text-red-200 text-[10px] space-y-0.5">
                  {data.errorMessages.map((message, index) => (
                    <li key={index}>• {message}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GenericBlock;
