import React, { useState } from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { GitBranch, Trash2, ChevronDown, HelpCircle } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface ConditionBlockProps {
  data: {
    id: string;
    operator: string;
    inputConnections: string[];
    trueConnection?: string;
    falseConnection?: string;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
    compareValue?: number;
    compareValue2?: number; // For 'between' operator
  };
  selected: boolean;
}

const ConditionBlock: React.FC<ConditionBlockProps> = ({ data, selected }) => {
  const [showHelp, setShowHelp] = useState(false); // Help section collapsed by default

  // Available operators
  const operators = [
    { value: '>', label: 'Greater Than (>)', description: 'True if A > B' },
    { value: '<', label: 'Less Than (<)', description: 'True if A < B' },
    { value: '==', label: 'Equal To (==)', description: 'True if A == B' },
    { value: '>=', label: 'Greater Than or Equal (>=)', description: 'True if A >= B' },
    { value: '<=', label: 'Less Than or Equal (<=)', description: 'True if A <= B' },
    { value: '!=', label: 'Not Equal (!=)', description: 'True if A != B' },
    { value: 'between', label: 'Between', description: 'True if A is between B and C' },
    { value: 'and', label: 'AND', description: 'True if both A and B are true' },
    { value: 'or', label: 'OR', description: 'True if either A or B is true' },
    { value: 'not', label: 'NOT', description: 'True if A is false' }
  ];

  // Handle operator change
  const handleOperatorChange = (value: string) => {
    data.onUpdate({ operator: value });
  };

  // Handle compare value change
  const handleCompareValueChange = (value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      data.onUpdate({ compareValue: numValue });
    }
  };

  // Handle second compare value change (for 'between' operator)
  const handleCompareValue2Change = (value: string) => {
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      data.onUpdate({ compareValue2: numValue });
    }
  };

  // Get the current operator
  const currentOperator = operators.find(op => op.value === data.operator) || operators[0];

  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center text-red-700 text-xs font-medium mb-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Error
        </div>
        <ul className="list-disc pl-5 text-xs text-red-600">
          {data.errorMessages.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: data.hasError ? '#ef4444' : '#6b7280',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* True output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          top: '35%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      {/* False output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        style={{
          background: data.hasError ? '#ef4444' : '#ef4444',
          top: '65%',
          width: '12px',
          height: '12px',
          border: '2px solid white'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <GitBranch className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Condition</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Operator</label>
              <Select
                value={data.operator}
                onValueChange={handleOperatorChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent>
                  {operators.map(op => (
                    <SelectItem key={op.value} value={op.value} className="text-xs">
                      {op.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Value input fields */}
            {['>', '<', '>=', '<=', '==', '!='].includes(data.operator) && (
              <div className="mt-2">
                <label className="text-xs font-medium block mb-1">Compare Value</label>
                <Input
                  type="number"
                  value={data.compareValue || 0}
                  onChange={e => handleCompareValueChange(e.target.value)}
                  className="h-8 text-xs"
                  step="any"
                />
              </div>
            )}

            {data.operator === 'between' && (
              <div className="mt-2 space-y-2">
                <div>
                  <label className="text-xs font-medium block mb-1">Minimum Value</label>
                  <Input
                    type="number"
                    value={data.compareValue || 0}
                    onChange={e => handleCompareValueChange(e.target.value)}
                    className="h-8 text-xs"
                    step="any"
                  />
                </div>
                <div>
                  <label className="text-xs font-medium block mb-1">Maximum Value</label>
                  <Input
                    type="number"
                    value={data.compareValue2 || 0}
                    onChange={e => handleCompareValue2Change(e.target.value)}
                    className="h-8 text-xs"
                    step="any"
                  />
                </div>
              </div>
            )}

            <Collapsible open={showHelp} onOpenChange={setShowHelp}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-between mt-2 h-8 text-xs text-muted-foreground hover:text-foreground"
                >
                  <div className="flex items-center gap-2">
                    <HelpCircle className="h-3 w-3" />
                    <span>How this block works</span>
                  </div>
                  <ChevronDown className={`h-3 w-3 transition-transform ${showHelp ? 'rotate-180' : ''}`} />
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="mt-2 p-3 bg-muted/30 rounded-md text-xs text-muted-foreground">
                  <p className="mb-2">
                    {currentOperator.description}
                  </p>

                  <div className="flex flex-col gap-2 mt-3">
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-green-500"></div>
                      <span>True output</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      <span>False output</span>
                    </div>
                  </div>

                  <div className="mt-3">
                    <p>Connect inputs to the left side and outputs to the right side.</p>
                    <p className="mt-1">This block evaluates a condition and routes the flow based on the result.</p>
                  </div>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Display error messages */}
            {renderErrorMessages()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ConditionBlock;
