import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { BarChart2, Trash2 } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface IndicatorBlockProps {
  data: {
    id: string;
    indicator: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const IndicatorBlock: React.FC<IndicatorBlockProps> = ({ data, selected }) => {

  // Available indicators
  const indicators = [
    { value: 'rsi', label: 'RSI', defaultParams: { period: 14 } },
    { value: 'macd', label: 'MACD', defaultParams: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9 } },
    { value: 'bollinger', label: 'Bollinger Bands', defaultParams: { period: 20, stdDev: 2 } },
    { value: 'sma', label: 'Simple Moving Average', defaultParams: { period: 20 } },
    { value: 'ema', label: 'Exponential Moving Average', defaultParams: { period: 20 } },
    { value: 'stochastic', label: 'Stochastic Oscillator', defaultParams: { kPeriod: 14, dPeriod: 3 } },
    { value: 'support', label: 'Support Level', defaultParams: { timeframe: 'month', strength: 2 } },
    { value: 'resistance', label: 'Resistance Level', defaultParams: { timeframe: 'month', strength: 2 } }
  ];

  // Get the current indicator (removed unused variable)

  // Handle indicator change
  const handleIndicatorChange = (value: string) => {
    const newIndicator = indicators.find(ind => ind.value === value);
    if (newIndicator) {
      data.onUpdate({
        indicator: value,
        parameters: newIndicator.defaultParams
      });
    }
  };

  // Handle parameter change
  const handleParameterChange = (key: string, value: string) => {
    // For timeframe and pattern, keep as string; for others, convert to number
    if (key === 'timeframe' || key === 'pattern') {
      data.onUpdate({
        parameters: {
          ...data.parameters,
          [key]: value
        }
      });
    } else {
      const numValue = parseFloat(value);
      if (!isNaN(numValue)) {
        data.onUpdate({
          parameters: {
            ...data.parameters,
            [key]: numValue
          }
        });
      }
    }
  };

  // Render parameters based on the selected indicator
  const renderParameters = () => {
    // Always show parameters since settings are always open

    switch (data.indicator) {
      case 'rsi':
        return (
          <div className="mt-2">
            <label className="text-xs font-medium block mb-1">Period</label>
            <Input
              type="number"
              value={data.parameters.period || 14}
              onChange={e => handleParameterChange('period', e.target.value)}
              className="h-8 text-xs"
              min={1}
            />
          </div>
        );
      case 'macd':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Fast Period</label>
              <Input
                type="number"
                value={data.parameters.fastPeriod || 12}
                onChange={e => handleParameterChange('fastPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Slow Period</label>
              <Input
                type="number"
                value={data.parameters.slowPeriod || 26}
                onChange={e => handleParameterChange('slowPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Signal Period</label>
              <Input
                type="number"
                value={data.parameters.signalPeriod || 9}
                onChange={e => handleParameterChange('signalPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
          </div>
        );
      case 'bollinger':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Period</label>
              <Input
                type="number"
                value={data.parameters.period || 20}
                onChange={e => handleParameterChange('period', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Standard Deviation</label>
              <Input
                type="number"
                value={data.parameters.stdDev || 2}
                onChange={e => handleParameterChange('stdDev', e.target.value)}
                className="h-8 text-xs"
                min={0.1}
                step={0.1}
              />
            </div>
          </div>
        );
      case 'sma':
      case 'ema':
        return (
          <div className="mt-2">
            <label className="text-xs font-medium block mb-1">Period</label>
            <Input
              type="number"
              value={data.parameters.period || 20}
              onChange={e => handleParameterChange('period', e.target.value)}
              className="h-8 text-xs"
              min={1}
            />
          </div>
        );
      case 'stochastic':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">%K Period</label>
              <Input
                type="number"
                value={data.parameters.kPeriod || 14}
                onChange={e => handleParameterChange('kPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">%D Period</label>
              <Input
                type="number"
                value={data.parameters.dPeriod || 3}
                onChange={e => handleParameterChange('dPeriod', e.target.value)}
                className="h-8 text-xs"
                min={1}
              />
            </div>
          </div>
        );
      case 'support':
      case 'resistance':
        return (
          <div className="space-y-2 mt-2">
            <div>
              <label className="text-xs font-medium block mb-1">Timeframe</label>
              <select
                value={data.parameters.timeframe || 'month'}
                onChange={e => handleParameterChange('timeframe', e.target.value)}
                className="w-full h-8 text-xs border border-input rounded-md px-2 bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
              >
                <option value="week">Last Week</option>
                <option value="month">Last Month</option>
                <option value="3month">Last 3 Months</option>
                <option value="6month">Last 6 Months</option>
                <option value="year">Last Year</option>
              </select>
              <p className="text-xs text-gray-500 mt-1">Historical period to analyze</p>
            </div>
            <div>
              <label className="text-xs font-medium block mb-1">Strength</label>
              <Input
                type="number"
                value={data.parameters.strength || 2}
                onChange={e => handleParameterChange('strength', e.target.value)}
                className="h-8 text-xs"
                min={2}
                max={10}
              />
              <p className="text-xs text-gray-500 mt-1">Minimum touches required</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  // Render error messages
  const renderErrorMessages = () => {
    if (!data.hasError || !data.errorMessages || data.errorMessages.length === 0) {
      return null;
    }

    return (
      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
        <div className="flex items-center text-red-700 text-xs font-medium mb-1">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Error
        </div>
        <ul className="list-disc pl-5 text-xs text-red-600">
          {data.errorMessages.map((error, index) => (
            <li key={index}>{error}</li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className={`relative ${data.isDisconnected ? 'disconnected-block' : ''}`}>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        style={{
          background: data.hasError ? '#ef4444' : '#555',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        style={{
          background: data.hasError ? '#ef4444' : '#22c55e',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          transformOrigin: '50% 50%'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart2 className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Technical Indicator</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                console.log('Delete button clicked for indicator block:', data.id);
                data.onRemove();
              }}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Indicator</label>
              <Select
                value={data.indicator}
                onValueChange={handleIndicatorChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select indicator" />
                </SelectTrigger>
                <SelectContent>
                  {indicators.map(indicator => (
                    <SelectItem key={indicator.value} value={indicator.value} className="text-xs">
                      {indicator.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {renderParameters()}

            {/* Display error messages */}
            {renderErrorMessages()}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IndicatorBlock;
