# Stripe Marketplace Setup Guide

This guide will help you set up Stripe Connect for the AI Agent Marketplace.

## 🔧 Prerequisites

1. **Stripe Account**: Create a Stripe account at [stripe.com](https://stripe.com)
2. **Supabase Project**: Your Supabase project should be set up and running
3. **Domain**: You'll need a domain for production (localhost works for testing)

## 📋 Step 1: Stripe Dashboard Setup

### 1.1 Get API Keys
1. Go to [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to **Developers** → **API Keys**
3. Copy your **Publishable key** and **Secret key** (use test keys for development)

### 1.2 Enable Stripe Connect
1. In Stripe Dashboard, go to **Connect** → **Settings**
2. Click **Get started** if you haven't enabled Connect yet
3. Fill out your platform information:
   - **Platform name**: "Trade Sensei AI Agent Marketplace"
   - **Platform website**: Your domain
   - **Support email**: Your support email

### 1.3 Configure Connect Settings
1. Go to **Connect** → **Settings** → **General**
2. Set **Account types**: Enable "Express accounts"
3. Set **Onboarding**: Enable "Collect tax information"
4. Set **Payouts**: Configure payout schedule (recommended: weekly)

## 🔑 Step 2: Environment Variables

### 🚨 SECURITY CRITICAL: Secret Key Management

**NEVER put secret keys in .env files!** Secret keys should ONLY be stored securely in Supabase Edge Functions.

### 2.1 Client-Side (.env.local) - PUBLISHABLE KEY ONLY
```bash
# ONLY the publishable key - safe for client-side use
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_51xxxxx...

# ❌ NEVER DO THIS:
# STRIPE_SECRET_KEY=sk_test_... ← NEVER PUT SECRET KEYS HERE!
```

### 2.2 Server-Side (Supabase Edge Functions) - SECRET KEYS
Secret keys are stored securely in Supabase and never exposed to the client:

**Option A: Via Supabase Dashboard**
1. Go to your Supabase Dashboard
2. Navigate to **Project Settings** → **Edge Functions**
3. Add secrets:
   - `STRIPE_SECRET_KEY`: Your Stripe secret key
   - `STRIPE_WEBHOOK_SECRET`: Your webhook secret

**Option B: Via CLI (Recommended)**
```bash
# Set secret keys securely via CLI
supabase secrets set STRIPE_SECRET_KEY=sk_test_your_key_here
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_your_secret_here
```

## 🔗 Step 3: Webhook Setup

### 3.1 Create Webhook Endpoint
1. In Stripe Dashboard, go to **Developers** → **Webhooks**
2. Click **Add endpoint**
3. Set endpoint URL: `https://your-project-ref.supabase.co/functions/v1/stripe-webhook`
4. Select events to listen for:
   - `payment_intent.succeeded`
   - `payment_intent.payment_failed`
   - `account.updated`
   - `transfer.created`

### 3.2 Get Webhook Secret
1. After creating the webhook, click on it
2. Copy the **Signing secret** (starts with `whsec_`)
3. Add this to your environment variables

## 🚀 Step 4: Deploy Edge Functions

### 4.1 Install Supabase CLI
```bash
npm install -g @supabase/cli
```

### 4.2 Login and Deploy
```bash
# Login to Supabase
supabase login

# Deploy the marketplace functions
supabase functions deploy marketplace-purchase
supabase functions deploy marketplace-stripe-connect
```

### 4.3 Set Secret Keys (CRITICAL SECURITY STEP)
```bash
# 🔒 SECURE: Set secret keys in Supabase Edge Functions only
supabase secrets set STRIPE_SECRET_KEY=sk_test_your_key_here
supabase secrets set STRIPE_WEBHOOK_SECRET=whsec_your_secret_here

# ✅ Verify secrets are set
supabase secrets list

# 🚨 NEVER commit secret keys to git or put them in .env files!
```

## 🧪 Step 5: Testing

### 5.1 Test Mode Setup
1. Use Stripe test keys (starting with `sk_test_` and `pk_test_`)
2. Use test card numbers from [Stripe Testing](https://stripe.com/docs/testing)
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`

### 5.2 Test the Flow
1. **Create Agent**: Create an agent and set a price
2. **Seller Setup**: Complete seller account setup (will redirect to Stripe)
3. **Purchase Flow**: Try purchasing an agent with test card
4. **Verify**: Check Stripe Dashboard for payments and transfers

## 🔒 Step 6: Production Setup

### 6.1 Switch to Live Mode
1. In Stripe Dashboard, toggle to **Live mode**
2. Get your live API keys
3. Update environment variables with live keys
4. Update webhook endpoint to production URL

### 6.2 Business Verification
1. Complete Stripe account verification
2. Provide business information
3. Set up bank account for payouts

## 💰 Platform Fee Configuration

The marketplace is configured with a **15% platform fee**:
- Buyer pays full price
- Seller receives 85% of the price
- Platform keeps 15% as fee

To change this, update the `platformFeePercentage` in:
- `supabase/functions/marketplace-purchase/index.ts`

## 🛠️ Troubleshooting

### Common Issues

1. **"Account not found" error**
   - Ensure seller has completed Stripe onboarding
   - Check that account is enabled for charges

2. **Payment fails**
   - Verify webhook is receiving events
   - Check Stripe Dashboard for error details
   - Ensure test cards are used in test mode

3. **Edge function errors**
   - Check Supabase function logs
   - Verify environment variables are set
   - Ensure Stripe keys are valid

### Debug Commands
```bash
# Check function logs
supabase functions logs marketplace-purchase

# Test function locally
supabase functions serve

# Check environment variables
supabase secrets list
```

## 📞 Support

- **Stripe Documentation**: [stripe.com/docs/connect](https://stripe.com/docs/connect)
- **Supabase Edge Functions**: [supabase.com/docs/guides/functions](https://supabase.com/docs/guides/functions)
- **Test Cards**: [stripe.com/docs/testing](https://stripe.com/docs/testing)

## 🔒 Security Best Practices

### Critical Security Rules:

1. **✅ DO**: Store secret keys in Supabase Edge Functions only
   ```bash
   supabase secrets set STRIPE_SECRET_KEY=sk_test_...
   ```

2. **❌ NEVER**: Put secret keys in .env files
   ```bash
   # ❌ WRONG - Never do this:
   STRIPE_SECRET_KEY=sk_test_...
   ```

3. **✅ DO**: Only put publishable keys in .env (they're safe for client-side)
   ```bash
   # ✅ CORRECT - Publishable keys are safe:
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
   ```

4. **✅ DO**: Add .env.local to .gitignore (should already be there)

5. **✅ DO**: Use different keys for development and production

6. **✅ DO**: Rotate keys if they're ever compromised

### Why This Matters:
- Secret keys can charge cards and access sensitive data
- If exposed, attackers could steal money or customer data
- Supabase Edge Functions provide secure server-side storage
- Client-side code is visible to users, server-side is not

## 🎉 You're Ready!

Once you've completed these steps, your AI Agent Marketplace will be fully functional with:
- ✅ Real Stripe payments
- ✅ Seller onboarding
- ✅ Automatic payouts
- ✅ Platform fee collection
- ✅ Purchase tracking
- ✅ Earnings dashboard

Happy selling! 🚀
