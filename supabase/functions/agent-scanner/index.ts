import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { SP500_STOCKS } from "../aura/data/sp500-stocks.ts"
import { RUSSELL2000_STOCKS } from "../aura/data/russell2000-stocks.ts"
import { NASDAQ_STOCKS } from "../aura/data/nasdaq-stocks.ts"
import { NASDAQ100_STOCKS } from "../aura/data/nasdaq100-stocks.ts"
import { ALL_STOCKS } from "../aura/data/all-stocks.ts"
import { executeAgent } from "../agent-runner/agent-executor.ts"
import { fetchHistoricalData } from "../aura/technical-analysis.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};



interface ScanRequest {
  agentId: string;
  marketIndex: string;
  userId: string;
}

interface ScanResult {
  symbol: string;
  signal: string;
  confidence: number;
  price: number;
  change: number;
  percentChange: number;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { agentId, marketIndex, userId }: ScanRequest = await req.json();

    // Validate input
    if (!agentId || !marketIndex || !userId) {
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    // Get the agent configuration
    const { data: agent, error: agentError } = await supabase
      .from('agents')
      .select('*')
      .eq('id', agentId)
      .eq('user_id', userId)
      .single();

    if (agentError || !agent) {
      return new Response(
        JSON.stringify({ error: 'Agent not found' }),
        {
          status: 404,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        }
      );
    }

    console.log(`Agent found: ${agent.name}`);
    console.log(`Agent configuration:`, {
      hasConfiguration: !!agent.configuration,
      blockCount: agent.configuration?.blocks?.length || 0,
      entryBlockId: agent.configuration?.entryBlockId,
      blockTypes: agent.configuration?.blocks?.map(b => b.type) || []
    });

    // Get stock list based on market index
    let stockList: string[] = [];
    switch (marketIndex) {
      case 'sp500':
        stockList = SP500_STOCKS;
        break;
      case 'nasdaq':
        stockList = NASDAQ_STOCKS;
        break;
      case 'nasdaq100':
        stockList = NASDAQ100_STOCKS;
        break;
      case 'russell2000':
        stockList = RUSSELL2000_STOCKS;
        break;
      case 'all':
        stockList = ALL_STOCKS;
        break;
      default:
        return new Response(
          JSON.stringify({ error: 'Invalid market index' }),
          {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          }
        );
    }

    console.log(`Scanning ${stockList.length} stocks with agent: ${agent.name}`);

    // Get Polygon API key for efficient bulk scanning
    const polygonApiKey = Deno.env.get('POLYGON_API_KEY') ?? '';

    console.log(`Using ${polygonApiKey ? 'live' : 'mock'} data for scanning`);

    // Calculate date range for historical data
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - 100 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // First, test the agent with a single stock to see if it works at all
    console.log(`Testing agent with AAPL first...`);
    try {
      let testDataResponse;
      try {
        testDataResponse = await fetchHistoricalData('AAPL', startDate, endDate, polygonApiKey);
      } catch (error) {
        console.error(`Error fetching AAPL data:`, error.message);
        throw new Error(`Failed to fetch test data for AAPL: ${error.message}`);
      }

      const testData = testDataResponse.data;
      if (testData && testData.length > 0) {
        // Create polygon data structure
        const polygonData = {
          price: {
            current: testData[testData.length - 1].close,
            open: testData[testData.length - 1].open,
            high: testData[testData.length - 1].high,
            low: testData[testData.length - 1].low,
            close: testData[testData.length - 1].close,
            volume: testData[testData.length - 1].volume,
            timestamp: testData[testData.length - 1].timestamp || new Date(testData[testData.length - 1].date).getTime()
          },
          historical: {
            open: testData.map(d => d.open),
            high: testData.map(d => d.high),
            low: testData.map(d => d.low),
            close: testData.map(d => d.close),
            volume: testData.map(d => d.volume),
            timestamp: testData.map(d => d.timestamp || new Date(d.date).getTime())
          },
          indicators: {},
          fundamentals: {}
        };

        // Calculate required indicators for test phase
        const { calculateIndicators } = await import("../agent-runner/indicators.ts");

        // Determine which indicators are needed
        const requiredIndicators = new Set<string>();
        for (const block of agent.configuration.blocks) {
          if (block.type === 'INDICATOR') {
            const indicatorName = (block as any).indicator;
            if (indicatorName) {
              requiredIndicators.add(indicatorName.toLowerCase());
            }
          } else if (block.type === 'MOMENTUM_INDICATOR') {
            const indicatorName = (block as any).indicator;
            if (indicatorName) {
              requiredIndicators.add(indicatorName.toLowerCase());
            }
          } else if (block.type === 'TREND_INDICATOR') {
            const indicatorName = (block as any).indicator;
            if (indicatorName) {
              requiredIndicators.add(indicatorName.toLowerCase());
            }
          } else if (block.type === 'VOLUME_INDICATOR') {
            const indicatorName = (block as any).indicator;
            if (indicatorName) {
              requiredIndicators.add(indicatorName.toLowerCase());
            }
          } else if (block.type === 'VOLATILITY_INDICATOR') {
            const indicatorName = (block as any).indicator;
            if (indicatorName) {
              requiredIndicators.add(indicatorName.toLowerCase());
            }
          }
        }

        // Calculate indicators if needed for test phase
        if (requiredIndicators.size > 0) {
          console.log(`Test phase: Setting up indicators: ${Array.from(requiredIndicators).join(', ')}`);
          for (const indicator of requiredIndicators) {
            if (indicator === 'support' || indicator === 'resistance' || indicator === 'candle_pattern' || indicator === 'rsi' || indicator === 'macd' || indicator === 'vwap') {
              // These are calculated in the executor
              polygonData.indicators[indicator] = 'calculated_in_executor';
              console.log(`Test phase: Set ${indicator} to 'calculated_in_executor'`);
            } else {
              try {
                const calculatedIndicators = calculateIndicators(polygonData.historical, [indicator]);
                Object.assign(polygonData.indicators, calculatedIndicators);
                console.log(`Test phase: Calculated ${indicator} indicator`);
              } catch (error) {
                console.error(`Test phase: Error calculating ${indicator}:`, error);
                polygonData.indicators[indicator] = [];
              }
            }
          }
        }

        try {
          const testResult = await executeAgent(agent.configuration, polygonData);
          console.log(`Test result for AAPL:`, {
            signal: testResult?.signal,
            confidence: testResult?.confidence,
            hasResult: !!testResult
          });
        } catch (testError) {
          console.error(`Error executing agent on test data:`, testError);
          console.error(`Test error stack:`, testError.stack);
          throw new Error(`Agent execution failed during test: ${testError.message}`);
        }
      }
    } catch (error) {
      console.error(`Test failed for AAPL:`, error);
    }

    const results: ScanResult[] = [];
    const batchSize = 10; // Process stocks in efficient batches
    let totalProcessed = 0;
    let totalBullish = 0;
    let totalBearish = 0;
    let totalNeutral = 0;
    let totalErrors = 0;

    // Process all stocks in the selected market index
    const scanLimit = stockList.length;
    console.log(`Processing ${scanLimit} stocks in batches of ${batchSize}`);

    // Process stocks in batches
    for (let i = 0; i < scanLimit; i += batchSize) {
      const batch = stockList.slice(i, i + batchSize);
      console.log(`Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.join(', ')}`);

      const batchPromises = batch.map(async (symbol) => {
        try {
          totalProcessed++;

          // Fetch historical data efficiently with fallback to mock data
          let historicalData;
          try {
            const dataResponse = await fetchHistoricalData(symbol, startDate, endDate, polygonApiKey);
            historicalData = dataResponse.data;
          } catch (error) {
            console.error(`Error fetching data for ${symbol}:`, error.message);
            return null; // Skip this symbol instead of using mock data
          }

          if (!historicalData || historicalData.length === 0) {
            console.warn(`No historical data for ${symbol}`);
            return null;
          }

          // Create polygon data structure from historical data
          const latestData = historicalData[historicalData.length - 1];
          const polygonData = {
            price: {
              current: latestData.close,
              open: latestData.open,
              high: latestData.high,
              low: latestData.low,
              close: latestData.close,
              volume: latestData.volume,
              timestamp: new Date(latestData.date).getTime()
            },
            historical: {
              open: historicalData.map(d => d.open),
              high: historicalData.map(d => d.high),
              low: historicalData.map(d => d.low),
              close: historicalData.map(d => d.close),
              volume: historicalData.map(d => d.volume),
              timestamp: historicalData.map(d => new Date(d.date).getTime())
            },
            indicators: {},
            fundamentals: {}
          };

          // Calculate required indicators
          const { calculateIndicators } = await import("../agent-runner/indicators.ts");

          // Determine which indicators are needed
          const requiredIndicators = new Set<string>();
          for (const block of agent.configuration.blocks) {
            if (block.type === 'INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'MOMENTUM_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'TREND_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'VOLUME_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            } else if (block.type === 'VOLATILITY_INDICATOR') {
              const indicatorName = (block as any).indicator;
              if (indicatorName) {
                requiredIndicators.add(indicatorName.toLowerCase());
              }
            }
          }

          // Calculate indicators if needed
          if (requiredIndicators.size > 0) {
            for (const indicator of requiredIndicators) {
              if (indicator === 'support' || indicator === 'resistance' || indicator === 'candle_pattern' || indicator === 'rsi' || indicator === 'macd' || indicator === 'vwap' || indicator === 'stochastic' || indicator === 'williams_r' || indicator === 'cci') {
                // These are calculated in the executor
                polygonData.indicators[indicator] = 'calculated_in_executor';
              } else {
                try {
                  const calculatedIndicators = calculateIndicators(polygonData.historical, [indicator]);
                  Object.assign(polygonData.indicators, calculatedIndicators);
                } catch (error) {
                  console.error(`Error calculating ${indicator} for ${symbol}:`, error);
                  polygonData.indicators[indicator] = [];
                }
              }
            }
          }

          // Execute the agent directly (much faster than edge function calls)
          console.log(`Executing agent for ${symbol}...`);
          let agentResult;
          try {
            agentResult = await executeAgent(agent.configuration, polygonData);
            console.log(`Agent execution completed for ${symbol}:`, {
              hasResult: !!agentResult,
              resultType: typeof agentResult,
              result: agentResult
            });
          } catch (agentError) {
            console.error(`Agent execution failed for ${symbol}:`, agentError);
            agentResult = null;
          }

          // Count all signals for debugging
          if (agentResult?.signal === 'bullish') {
            totalBullish++;
          } else if (agentResult?.signal === 'bearish') {
            totalBearish++;
          } else if (agentResult?.signal === 'neutral') {
            totalNeutral++;
          }

          console.log(`${symbol}: ${agentResult.signal} (${agentResult.confidence}%)`);
          console.log(`Agent result details:`, {
            symbol,
            hasResult: !!agentResult,
            signal: agentResult?.signal,
            confidence: agentResult?.confidence,
            confidenceType: typeof agentResult?.confidence,
            isBullish: agentResult?.signal === 'bullish',
            confidenceCheck: agentResult?.confidence > 50,
            allConditions: agentResult && agentResult.signal === 'bullish' && agentResult.confidence > 50
          });

          if (agentResult && agentResult.signal === 'bullish' && agentResult.confidence > 50) {
            console.log(`✅ ${symbol} PASSED filter - adding to results`);
            const previousData = historicalData[historicalData.length - 2];
            const change = previousData ? latestData.close - previousData.close : 0;
            const percentChange = previousData ? ((latestData.close - previousData.close) / previousData.close) * 100 : 0;

            const result = {
              symbol: symbol,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              price: latestData.close,
              change: change,
              percentChange: percentChange
            };
            console.log(`Result object for ${symbol}:`, result);
            return result;
          } else {
            console.log(`❌ ${symbol} FAILED filter - not adding to results`);
          }

          return null;
        } catch (error) {
          totalErrors++;
          console.error(`Error processing ${symbol}:`, error);
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      console.log(`Batch ${Math.floor(i / batchSize) + 1} completed:`, {
        totalPromises: batchPromises.length,
        totalResults: batchResults.length,
        nullResults: batchResults.filter(r => r === null).length,
        validResults: batchResults.filter(r => r !== null).length
      });

      const validResults = batchResults.filter(result => result !== null) as ScanResult[];
      console.log(`Valid results from batch:`, validResults.map(r => `${r.symbol}: ${r.signal} (${r.confidence}%)`));
      results.push(...validResults);
      console.log(`Total results so far: ${results.length}`);

      // Add a small delay between batches to avoid overwhelming the API
      if (i + batchSize < scanLimit) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }

    // Sort results by confidence (highest first)
    results.sort((a, b) => b.confidence - a.confidence);

    // Return all results (no artificial limit)
    const limitedResults = results;

    console.log(`Scanning Summary:
      - Total Processed: ${totalProcessed}
      - Total Bullish: ${totalBullish}
      - Total Bearish: ${totalBearish}
      - Total Neutral: ${totalNeutral}
      - Total Errors: ${totalErrors}
      - Final Results: ${limitedResults.length}
    `);

    return new Response(
      JSON.stringify({
        results: limitedResults,
        stocksScanned: totalProcessed, // Add stocksScanned for time tracking
        totalScanned: totalProcessed,
        marketIndex: marketIndex,
        agentName: agent.name
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error) {
    console.error('Agent scanner error:', error);
    console.error('Error stack:', error.stack);
    console.error('Error message:', error.message);

    return new Response(
      JSON.stringify({
        error: 'Internal server error',
        details: error.message,
        stack: error.stack
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
