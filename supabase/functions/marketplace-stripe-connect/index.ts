import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "https://esm.sh/stripe@14.21.0"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// Initialize Stripe client
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY_TEST') || '', {
  apiVersion: '2023-10-16',
});

interface CreateAccountRequest {
  action: 'create-account';
  email: string;
  country?: string;
}

interface CreateAccountLinkRequest {
  action: 'create-account-link';
  account_id: string;
  refresh_url: string;
  return_url: string;
}

interface GetAccountRequest {
  action: 'get-account';
  account_id?: string;
}

type RequestBody = CreateAccountRequest | CreateAccountLinkRequest | GetAccountRequest;

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Get the authenticated user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication');
    }

    const requestData: RequestBody = await req.json();

    switch (requestData.action) {
      case 'create-account': {
        console.log(`Creating Stripe Connect account for user ${user.id}`);

        // Check if user already has a seller account
        const { data: existingAccount } = await supabase
          .from('seller_accounts')
          .select('*')
          .eq('user_id', user.id)
          .single();

        if (existingAccount) {
          // Verify the account still exists in Stripe
          try {
            const stripeAccount = await stripe.accounts.retrieve(existingAccount.stripe_account_id);
            console.log('Existing account verified in Stripe:', stripeAccount.id);

            return new Response(JSON.stringify({
              success: true,
              account_id: existingAccount.stripe_account_id,
              status: existingAccount.account_status,
              message: 'Account already exists'
            }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
          } catch (stripeError) {
            console.log('Existing account not found in Stripe, creating new one:', stripeError.message);
            // Delete the invalid record and create a new one
            await supabase
              .from('seller_accounts')
              .delete()
              .eq('user_id', user.id);
          }
        }

        // Create real Stripe Connect account
        const stripeAccount = await stripe.accounts.create({
          type: 'express',
          country: requestData.country || 'US',
          email: requestData.email,
          capabilities: {
            card_payments: { requested: true },
            transfers: { requested: true },
          },
          business_type: 'individual',
          settings: {
            payouts: {
              schedule: {
                interval: 'weekly',
                weekly_anchor: 'friday'
              }
            }
          }
        });

        // Store account in database
        const { error: dbError } = await supabase
          .from('seller_accounts')
          .insert({
            user_id: user.id,
            stripe_account_id: stripeAccount.id,
            account_status: 'pending',
            charges_enabled: stripeAccount.charges_enabled || false,
            payouts_enabled: stripeAccount.payouts_enabled || false,
            details_submitted: stripeAccount.details_submitted || false,
            requirements_due: stripeAccount.requirements?.currently_due || []
          });

        if (dbError) {
          throw new Error(`Failed to store account: ${dbError.message}`);
        }

        return new Response(JSON.stringify({
          success: true,
          account_id: stripeAccount.id,
          status: 'pending',
          message: 'Stripe Connect account created successfully'
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'create-account-link': {
        console.log(`Creating account link for account ${requestData.account_id}`);

        // Verify the account belongs to the user
        console.log(`Looking for account ${requestData.account_id} for user ${user.id}`);

        const { data: sellerAccount, error: accountError } = await supabase
          .from('seller_accounts')
          .select('*')
          .eq('user_id', user.id)
          .eq('stripe_account_id', requestData.account_id)
          .single();

        console.log('Database query result:', { sellerAccount, accountError });

        if (accountError || !sellerAccount) {
          console.error('Account verification failed:', { accountError, sellerAccount });
          throw new Error(`Account not found or access denied: ${accountError?.message || 'No account found'}`);
        }

        // Create real Stripe account link
        console.log('Creating Stripe account link with params:', {
          account: requestData.account_id,
          refresh_url: requestData.refresh_url,
          return_url: requestData.return_url,
          type: 'account_onboarding',
        });

        let accountLink;
        try {
          accountLink = await stripe.accountLinks.create({
            account: requestData.account_id,
            refresh_url: requestData.refresh_url,
            return_url: requestData.return_url,
            type: 'account_onboarding',
          });

          console.log('Stripe account link created successfully:', accountLink.url);
        } catch (stripeError) {
          console.error('Stripe API error:', stripeError);
          throw new Error(`Stripe error: ${stripeError.message}`);
        }

        return new Response(JSON.stringify({
          success: true,
          url: accountLink.url
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'get-account': {
        const accountId = requestData.account_id;
        
        // If no account_id provided, get user's account
        if (!accountId) {
          const { data: sellerAccount } = await supabase
            .from('seller_accounts')
            .select('*')
            .eq('user_id', user.id)
            .single();

          if (!sellerAccount) {
            return new Response(JSON.stringify({
              success: false,
              message: 'No seller account found'
            }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' }
            });
          }

          // Get fresh data from Stripe
          const stripeAccount = await stripe.accounts.retrieve(sellerAccount.stripe_account_id);

          // Update database with latest info
          await supabase
            .from('seller_accounts')
            .update({
              charges_enabled: stripeAccount.charges_enabled || false,
              payouts_enabled: stripeAccount.payouts_enabled || false,
              details_submitted: stripeAccount.details_submitted || false,
              requirements_due: stripeAccount.requirements?.currently_due || [],
              account_status: stripeAccount.details_submitted ? 'active' : 'pending',
              updated_at: new Date().toISOString()
            })
            .eq('user_id', user.id);

          return new Response(JSON.stringify({
            success: true,
            account: {
              id: stripeAccount.id,
              stripe_account_id: stripeAccount.id,
              charges_enabled: stripeAccount.charges_enabled || false,
              payouts_enabled: stripeAccount.payouts_enabled || false,
              details_submitted: stripeAccount.details_submitted || false,
              requirements: stripeAccount.requirements?.currently_due || [],
              account_status: stripeAccount.details_submitted ? 'active' : 'pending'
            }
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          });
        }

        // Verify access to specific account
        const { data: sellerAccount } = await supabase
          .from('seller_accounts')
          .select('*')
          .eq('user_id', user.id)
          .eq('stripe_account_id', accountId)
          .single();

        if (!sellerAccount) {
          throw new Error('Account not found or access denied');
        }

        return new Response(JSON.stringify({
          success: true,
          account: {
            id: sellerAccount.stripe_account_id,
            stripe_account_id: sellerAccount.stripe_account_id,
            charges_enabled: sellerAccount.charges_enabled,
            payouts_enabled: sellerAccount.payouts_enabled,
            details_submitted: sellerAccount.details_submitted,
            account_status: sellerAccount.account_status,
            requirements: []
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'create-dashboard-link': {
        const accountId = requestData.account_id;

        if (!accountId) {
          throw new Error('Account ID is required');
        }

        // Verify the account belongs to the user
        console.log(`Creating dashboard link for account ${accountId} for user ${user.id}`);

        const { data: sellerAccount, error: accountError } = await supabase
          .from('seller_accounts')
          .select('*')
          .eq('user_id', user.id)
          .eq('stripe_account_id', accountId)
          .single();

        if (accountError || !sellerAccount) {
          console.error('Account verification failed:', { accountError, sellerAccount });
          throw new Error(`Account not found or access denied: ${accountError?.message || 'No account found'}`);
        }

        // Create Stripe Express Dashboard link
        console.log('Creating Stripe Express Dashboard link for account:', accountId);

        try {
          const dashboardLink = await stripe.accounts.createLoginLink(accountId);

          console.log('Stripe Express Dashboard link created successfully:', dashboardLink.url);

          return new Response(JSON.stringify({
            success: true,
            url: dashboardLink.url
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          });
        } catch (stripeError) {
          console.error('Stripe API error:', stripeError);
          throw new Error(`Stripe error: ${stripeError.message}`);
        }
      }

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Marketplace Stripe Connect error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
