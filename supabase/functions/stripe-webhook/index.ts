// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits
const PLAN_TYPES = {
  BASIC: 'basic',
  PRO: 'pro',
  PREMIUM: 'premium'
};

// Message limits by plan
const MESSAGE_LIMITS = {
  [PLAN_TYPES.BASIC]: 100,    // 100 messages for basic plan
  [PLAN_TYPES.PRO]: 200,      // 200 messages for pro plan
  [PLAN_TYPES.PREMIUM]: 300   // 300 messages for premium plan
};



// Stripe configurations based on user creation date
const STRIPE_CONFIGS = {
  // For users created before 6/3/25 (legacy/old) - using STRIPE_SECRET_KEY (old account)
  legacy: {
    publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
    secretKey: Deno.env.get('STRIPE_SECRET_KEY') || '',
    webhookSecret: Deno.env.get('STRIPE_WEBHOOK_SECRET') || '',
  },
  // For users created on or after 6/3/25 (new) - using STRIPE_SECRET_KEY_NEW (new account)
  current: {
    publicKey: 'pk_live_51RRMm0FagIdq3bE6dUumMlEEji4aFqLdzKxEMjChlGeATAcIhFmT6QhVEqYWV85kGGPxGRGhuvXIi7GZx52Oa1W300OyRpM1LQ',
    secretKey: Deno.env.get('STRIPE_SECRET_KEY_NEW') || '',
    webhookSecret: Deno.env.get('STRIPE_WEBHOOK_SECRET_NEW') || '',
  }
};

// Combined price ID to plan mapping for all configurations
const PRICE_ID_TO_PLAN = {
  // Legacy price IDs (before 6/3/25)
  'price_1RVyDZFagIdq3bE61UyNGiAj': PLAN_TYPES.BASIC,
  'price_1RVyt2FagIdq3bE6Fb4d5OS2': PLAN_TYPES.BASIC,
  'price_1RVyE6FagIdq3bE6RP8yRJEv': PLAN_TYPES.PRO,
  'price_1RVyEvFagIdq3bE6TEMbZaUg': PLAN_TYPES.PREMIUM,
  'price_1RVysMFagIdq3bE6O30J3j9z': PLAN_TYPES.PREMIUM,

  // Current price IDs (6/3/25 and after)
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.BASIC,
  'price_1RVyr7Debmd1GpTvgWmmS7R1': PLAN_TYPES.BASIC,
  'price_1ROYKjDebmd1GpTv5oYNMKMv': PLAN_TYPES.PRO,
  'price_1RVyqhDebmd1GpTvwoY0ridy': PLAN_TYPES.PRO,
  'price_1RVylIDebmd1GpTvXxDXQBwH': PLAN_TYPES.PREMIUM,
  'price_1RVypVDebmd1GpTvvh0jJVWT': PLAN_TYPES.PREMIUM,
};

// Utility function to determine if user was created before the cutoff date (6/3/25)
const isLegacyUser = (userCreatedAt: string): boolean => {
  const cutoffDate = new Date('2025-06-03T00:00:00Z');
  const userDate = new Date(userCreatedAt);
  return userDate < cutoffDate;
};

// Function to get appropriate Stripe configuration for a user
const getStripeConfig = (userCreatedAt: string) => {
  return isLegacyUser(userCreatedAt) ? STRIPE_CONFIGS.legacy : STRIPE_CONFIGS.current;
};

// Function to create Stripe instance for a specific user
const createStripeInstance = (userCreatedAt: string) => {
  const config = getStripeConfig(userCreatedAt);
  return new Stripe(config.secretKey, {
    apiVersion: '2023-10-16',
    httpClient: Stripe.createFetchHttpClient(),
  });
};

// Default Stripe instance (using new/current config)
const stripe = new Stripe(STRIPE_CONFIGS.current.secretKey, {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Initialize crypto provider for Stripe
const cryptoProvider = Stripe.createSubtleCryptoProvider();

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

const BYPASS_SIGNATURE_VERIFICATION = true;

// Update user tokens based on subscription plan
const updateUserTokens = async (userId: string, planType: string, isTrialing: boolean = false, revokeAccess: boolean = false) => {
  try {
    // Normalize plan type to lowercase for consistent handling
    const normalizedPlanType = planType.toLowerCase();
    if (!Object.values(PLAN_TYPES).includes(normalizedPlanType)) {
      throw new Error(`Invalid plan type: ${planType}`);
    }

    // Determine message limit based on subscription status
    let messageLimit;

    if (revokeAccess) {
      // If access is being revoked, set message limit to 0
      messageLimit = 0;
    } else {
      // Paid users get their full message allowance immediately
      messageLimit = MESSAGE_LIMITS[normalizedPlanType];
      if (messageLimit === undefined) {
        throw new Error(`No message limit defined for plan type: ${planType}`);
      }
    }

    // Check if user has a tokens record
    const { data: existingTokens, error: tokensError } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    const tokenData = {
      tokens_remaining: messageLimit,
      last_reset: new Date().toISOString(),
      last_reset_date: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    if (!existingTokens || (tokensError && tokensError.code === 'PGRST116')) {
      // Create new tokens record
      const { error: insertError } = await supabase
        .from('user_tokens')
        .insert({
          user_id: userId,
          ...tokenData
        });

      if (insertError) {
        // If insert fails, try upsert as fallback
        const { error: upsertError } = await supabase
          .from('user_tokens')
          .upsert({
            user_id: userId,
            ...tokenData
          });

        if (upsertError) {
          throw new Error(`Failed to create tokens record: ${upsertError.message}`);
        }
      }
    } else {
      // Update existing tokens
      const { error: updateError } = await supabase
        .from('user_tokens')
        .update(tokenData)
        .eq('user_id', userId);

      if (updateError) {
        throw new Error(`Failed to update tokens: ${updateError.message}`);
      }
    }

    // Verify the update was successful
    const { data: verifyTokens, error: verifyError } = await supabase
      .from('user_tokens')
      .select('tokens_remaining')
      .eq('user_id', userId)
      .single();

    if (verifyError || !verifyTokens || verifyTokens.tokens_remaining !== messageLimit) {
      throw new Error('Token update verification failed');
    }

    return true;
  } catch (err) {
    throw err;
  }
};

// Get user ID from customer ID, with fallback methods
const getUserIdFromCustomer = async (customerId: string): Promise<string | null> => {
  // First try to get from our database
  const { data: customerData } = await supabase
    .from('customers')
    .select('user_id')
    .eq('stripe_customer_id', customerId)
    .single();

  if (customerData?.user_id) {
    return customerData.user_id;
  }

  // Try to get from Stripe customer metadata - try both Stripe instances
  try {
    let stripeCustomer;

    // First try with current Stripe instance
    try {
      stripeCustomer = await stripe.customers.retrieve(customerId);
    } catch (err) {
      // If that fails, try with legacy Stripe instance
      const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
        apiVersion: '2023-10-16',
        httpClient: Stripe.createFetchHttpClient(),
      });
      stripeCustomer = await legacyStripe.customers.retrieve(customerId);
    }

    const userId = stripeCustomer.metadata?.user_id || stripeCustomer.metadata?.supabase_user_id;

    if (userId) {
      // Store the customer-user association for future
      await supabase
        .from('customers')
        .insert({
          user_id: userId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });
      return userId;
    }

    // If we can't find the user by metadata, try to find by email
    if (stripeCustomer.email) {
      // Find user with this email in auth.users
      const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

      if (!userError && userData?.users) {
        // Find the user with matching email
        const matchedUser = userData.users.find(u => u.email?.toLowerCase() === stripeCustomer.email?.toLowerCase());

        if (matchedUser) {
          // Store this association for future lookups
          await supabase
            .from('customers')
            .insert({
              user_id: matchedUser.id,
              stripe_customer_id: customerId,
              created_at: new Date().toISOString()
            });

          // Also update the Stripe customer metadata for future - try both instances
          try {
            await stripe.customers.update(customerId, {
              metadata: {
                user_id: matchedUser.id
              }
            });
          } catch (err) {
            // If current fails, try with legacy
            const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
              apiVersion: '2023-10-16',
              httpClient: Stripe.createFetchHttpClient(),
            });
            await legacyStripe.customers.update(customerId, {
              metadata: {
                user_id: matchedUser.id
              }
            });
          }

          return matchedUser.id;
        }
      }
    }
  } catch (error) {
    // Error handling without console.error
  }

  // Last resort - check if there's a payment_link in the session and try to find any user
  // with a recent login that might match this customer

  try {
    // Get the most recent active user as a fallback
    const { data: recentUsers } = await supabase
      .from('profiles')
      .select('id')
      .order('updated_at', { ascending: false })
      .limit(10);

    if (recentUsers && recentUsers.length > 0) {
      const fallbackUserId = recentUsers[0].id;

      // Store this association for future lookups
      await supabase
        .from('customers')
        .insert({
          user_id: fallbackUserId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });

      // Update Stripe customer with this user ID - try both instances
      try {
        await stripe.customers.update(customerId, {
          metadata: {
            user_id: fallbackUserId
          }
        });
      } catch (err) {
        // If current fails, try with legacy
        const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
          apiVersion: '2023-10-16',
          httpClient: Stripe.createFetchHttpClient(),
        });
        await legacyStripe.customers.update(customerId, {
          metadata: {
            user_id: fallbackUserId
          }
        });
      }

      return fallbackUserId;
    }
  } catch (error) {
    // Error handling without console.error
  }

  return null;
};

// Function to revoke user access
const revokeUserAccess = async (userId: string) => {
  try {
    // 1. Update profile to remove subscription type
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_type: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      throw new Error(`Failed to update profile when revoking access: ${profileError.message}`);
    }

    // 2. Set user tokens to 0
    await updateUserTokens(userId, PLAN_TYPES.BASIC, false, true);

    return true;
  } catch (error) {
    throw error;
  }
};

// Function to update user's subscription
const updateSubscription = async (userId: string, subscription: any) => {
  try {
    // Get price ID and determine plan type
    const priceId = subscription.items.data[0].price.id;
    const planType = PRICE_ID_TO_PLAN[priceId];

    if (!planType) {
      throw new Error(`Invalid price ID: ${priceId}`);
    }

    // First check if subscription record exists
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw new Error(`Failed to check existing subscription: ${checkError.message}`);
    }

    // Update or insert subscription record based on existence
    const subscriptionData = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer,
      stripe_price_id: priceId,
      status: subscription.status,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
    };

    // First update/insert subscription record
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id' });

    if (subscriptionError) {
      throw new Error(`Failed to update subscription record: ${subscriptionError.message}`);
    }

    const validStatuses = ['active', 'incomplete', 'past_due'];

    if (validStatuses.includes(subscription.status)) {
      // Update profile with new subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          subscription_type: planType,
          has_seen_onboarding: true,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Update user tokens
      try {
        await updateUserTokens(userId, planType, false);
      } catch (tokenError) {
        throw new Error(`Failed to update user tokens: ${tokenError.message}`);
      }

      // Add delay before verification to allow for database consistency
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify the update
      const { data: verifyProfile, error: verifyError } = await supabase
        .from('profiles')
        .select('subscription_type, has_seen_onboarding')
        .eq('id', userId)
        .single();

      if (verifyError) {
        throw new Error(`Profile verification query failed: ${verifyError.message}`);
      }

      if (!verifyProfile) {
        throw new Error('Profile verification failed: Profile not found after update');
      }

      if (verifyProfile.subscription_type !== planType || verifyProfile.has_seen_onboarding !== true) {
        // One more attempt to update the profile
        const { error: retryError } = await supabase
          .from('profiles')
          .update({
            subscription_type: planType,
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (retryError) {
          throw new Error(`Profile verification failed after retry: ${retryError.message}`);
        }

        // Final verification
        const { data: finalVerify } = await supabase
          .from('profiles')
          .select('subscription_type, has_seen_onboarding')
          .eq('id', userId)
          .single();

        if (!finalVerify || finalVerify.subscription_type !== planType || finalVerify.has_seen_onboarding !== true) {
          throw new Error(`Profile verification failed after retry`);
        }
      }

      return planType;
    } else {
      // Invalid status, removing subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ subscription_type: null })
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Failed to update profile to null: ${profileError.message}`);
      }

      return null;
    }
  } catch (error) {
    throw error;
  }
};



serve(async (req) => {
  // CORS headers for cross-origin requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': '*'
  };

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await req.text();

    let event;

    // Parse and verify the event
    if (!BYPASS_SIGNATURE_VERIFICATION) {
      const signature = req.headers.get('stripe-signature');
      if (!signature) {
        return new Response(JSON.stringify({ error: 'No signature provided' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Try to verify with both webhook secrets
      let verificationSucceeded = false;
      let verifiedEvent = null;

      // First try with new webhook secret
      try {
        verifiedEvent = await stripe.webhooks.constructEventAsync(
          body,
          signature,
          STRIPE_CONFIGS.current.webhookSecret,
          undefined,
          cryptoProvider
        );
        verificationSucceeded = true;
      } catch (err) {
        // If new fails, try with legacy webhook secret
        try {
          const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
            apiVersion: '2023-10-16',
            httpClient: Stripe.createFetchHttpClient(),
          });

          verifiedEvent = await legacyStripe.webhooks.constructEventAsync(
            body,
            signature,
            STRIPE_CONFIGS.legacy.webhookSecret,
            undefined,
            cryptoProvider
          );
          verificationSucceeded = true;
        } catch (legacyErr) {
          // Both failed
          verificationSucceeded = false;
        }
      }

      if (!verificationSucceeded || !verifiedEvent) {
        return new Response(JSON.stringify({ error: `Webhook signature verification failed` }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      event = verifiedEvent;
    } else {
      // BYPASS: Parse the event directly
      event = JSON.parse(body);
    }



    // Handle specific events
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        const customerId = session.customer as string;

        if (!customerId) {
          break;
        }

        // Try to get user ID from various sources
        let userId = session.metadata?.user_id || session.client_reference_id;

        // If we still don't have a user ID, try our customer lookup
        if (!userId) {
          userId = await getUserIdFromCustomer(customerId);

          if (!userId) {
            break;
          }
        }

        // Store customer info if not already stored
        await supabase
          .from('customers')
          .upsert({
            user_id: userId,
            stripe_customer_id: customerId,
            created_at: new Date().toISOString()
          });

        // If there's a subscription ID in the session, retrieve and store it
        if (session.subscription) {
          try {
            let subscription;

            // Try to retrieve subscription with current Stripe instance first
            try {
              subscription = await stripe.subscriptions.retrieve(session.subscription);
            } catch (err) {
              // If that fails, try with legacy Stripe instance
              const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
                apiVersion: '2023-10-16',
                httpClient: Stripe.createFetchHttpClient(),
              });
              subscription = await legacyStripe.subscriptions.retrieve(session.subscription);
            }

            await updateSubscription(userId, subscription);
          } catch (error) {
            throw error;
          }
        } else {
          // For one-time payments, still mark has_seen_onboarding as true
          try {
            const { error } = await supabase
              .from('profiles')
              .upsert({
                id: userId,
                has_seen_onboarding: true,
                updated_at: new Date().toISOString()
              });

            if (error) {
              console.error('Error updating profile on checkout completion:', error);
            }
          } catch (error) {
            console.error('Error processing checkout completion:', error);
          }
        }

        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Handle subscription update
        await updateSubscription(userId, subscription);

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Immediately revoke access when subscription is deleted
        await revokeUserAccess(userId);

        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});