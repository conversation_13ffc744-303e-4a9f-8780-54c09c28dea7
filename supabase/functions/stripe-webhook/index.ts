// Setup type definitions for built-in Supabase Runtime APIs
import "jsr:@supabase/functions-js/edge-runtime.d.ts"
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Constants for plans and limits - Premium only
const PLAN_TYPES = {
  PREMIUM: 'premium'
};

// Message limits by plan - Unlimited for Premium
const MESSAGE_LIMITS = {
  [PLAN_TYPES.PREMIUM]: 999999   // Effectively unlimited messages for premium plan
};



// Stripe configurations - Using only the old/legacy account for all users
const STRIPE_CONFIGS = {
  // Using STRIPE_SECRET_KEY (old account) for all users - generates #fidpam URLs
  legacy: {
    publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
    secretKey: Deno.env.get('STRIPE_SECRET_KEY') || '',
    webhookSecret: Deno.env.get('STRIPE_WEBHOOK_SECRET') || '',
  }
};

// Price ID to plan mapping - Using only legacy account price IDs for all users
const PRICE_ID_TO_PLAN = {
  // Legacy account price IDs - These generate URLs with #fidpam
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.PREMIUM, // Weekly Premium
  'price_1RVyr7Debmd1GpTvgWmmS7R1': PLAN_TYPES.PREMIUM, // Annual Premium
};

// Utility function - Always return true since we use legacy account for all users now
const isLegacyUser = (userCreatedAt: string): boolean => {
  return true; // All users use legacy account now to get #fidpam URLs
};

// Function to get appropriate Stripe configuration for a user - Always use legacy now
const getStripeConfig = (userCreatedAt: string) => {
  return STRIPE_CONFIGS.legacy; // Always use legacy account for all users
};

// Function to create Stripe instance for a specific user
const createStripeInstance = (userCreatedAt: string) => {
  const config = getStripeConfig(userCreatedAt);
  return new Stripe(config.secretKey, {
    apiVersion: '2023-10-16',
    httpClient: Stripe.createFetchHttpClient(),
  });
};

// Default Stripe instance (using test key for marketplace)
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY_TEST') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Initialize crypto provider for Stripe
const cryptoProvider = Stripe.createSubtleCryptoProvider();

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

const BYPASS_SIGNATURE_VERIFICATION = true;

// Marketplace-specific Stripe instance
const marketplaceStripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY_TEST') || '', {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// Update user tokens based on subscription plan
const updateUserTokens = async (userId: string, planType: string, isTrialing: boolean = false, revokeAccess: boolean = false) => {
  try {
    // Normalize plan type to lowercase for consistent handling
    const normalizedPlanType = planType.toLowerCase();
    if (!Object.values(PLAN_TYPES).includes(normalizedPlanType)) {
      throw new Error(`Invalid plan type: ${planType}`);
    }

    // Determine message limit based on subscription status
    let messageLimit;

    if (revokeAccess) {
      // If access is being revoked, set message limit to 0
      messageLimit = 0;
    } else {
      // Paid users get their full message allowance immediately
      messageLimit = MESSAGE_LIMITS[normalizedPlanType];
      if (messageLimit === undefined) {
        throw new Error(`No message limit defined for plan type: ${planType}`);
      }
    }

    // Check if user has a tokens record
    const { data: existingTokens, error: tokensError } = await supabase
      .from('user_tokens')
      .select('*')
      .eq('user_id', userId)
      .single();

    const tokenData = {
      tokens_remaining: messageLimit,
      last_reset: new Date().toISOString(),
      last_reset_date: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    if (!existingTokens || (tokensError && tokensError.code === 'PGRST116')) {
      // Create new tokens record
      const { error: insertError } = await supabase
        .from('user_tokens')
        .insert({
          user_id: userId,
          ...tokenData
        });

      if (insertError) {
        // If insert fails, try upsert as fallback
        const { error: upsertError } = await supabase
          .from('user_tokens')
          .upsert({
            user_id: userId,
            ...tokenData
          });

        if (upsertError) {
          throw new Error(`Failed to create tokens record: ${upsertError.message}`);
        }
      }
    } else {
      // Update existing tokens
      const { error: updateError } = await supabase
        .from('user_tokens')
        .update(tokenData)
        .eq('user_id', userId);

      if (updateError) {
        throw new Error(`Failed to update tokens: ${updateError.message}`);
      }
    }

    // Verify the update was successful
    const { data: verifyTokens, error: verifyError } = await supabase
      .from('user_tokens')
      .select('tokens_remaining')
      .eq('user_id', userId)
      .single();

    if (verifyError || !verifyTokens || verifyTokens.tokens_remaining !== messageLimit) {
      throw new Error('Token update verification failed');
    }

    return true;
  } catch (err) {
    throw err;
  }
};

// Get user ID from customer ID, with fallback methods
const getUserIdFromCustomer = async (customerId: string): Promise<string | null> => {
  // First try to get from our database
  const { data: customerData } = await supabase
    .from('customers')
    .select('user_id')
    .eq('stripe_customer_id', customerId)
    .single();

  if (customerData?.user_id) {
    return customerData.user_id;
  }

  // Try to get from Stripe customer metadata - try both Stripe instances
  try {
    let stripeCustomer;

    // First try with current Stripe instance
    try {
      stripeCustomer = await stripe.customers.retrieve(customerId);
    } catch (err) {
      // If that fails, try with legacy Stripe instance
      const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
        apiVersion: '2023-10-16',
        httpClient: Stripe.createFetchHttpClient(),
      });
      stripeCustomer = await legacyStripe.customers.retrieve(customerId);
    }

    const userId = stripeCustomer.metadata?.user_id || stripeCustomer.metadata?.supabase_user_id;

    if (userId) {
      // Store the customer-user association for future
      await supabase
        .from('customers')
        .insert({
          user_id: userId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });
      return userId;
    }

    // If we can't find the user by metadata, try to find by email
    if (stripeCustomer.email) {
      // Find user with this email in auth.users
      const { data: userData, error: userError } = await supabase.auth.admin.listUsers();

      if (!userError && userData?.users) {
        // Find the user with matching email
        const matchedUser = userData.users.find(u => u.email?.toLowerCase() === stripeCustomer.email?.toLowerCase());

        if (matchedUser) {
          // Store this association for future lookups
          await supabase
            .from('customers')
            .insert({
              user_id: matchedUser.id,
              stripe_customer_id: customerId,
              created_at: new Date().toISOString()
            });

          // Also update the Stripe customer metadata for future - try both instances
          try {
            await stripe.customers.update(customerId, {
              metadata: {
                user_id: matchedUser.id
              }
            });
          } catch (err) {
            // If current fails, try with legacy
            const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
              apiVersion: '2023-10-16',
              httpClient: Stripe.createFetchHttpClient(),
            });
            await legacyStripe.customers.update(customerId, {
              metadata: {
                user_id: matchedUser.id
              }
            });
          }

          return matchedUser.id;
        }
      }
    }
  } catch (error) {
    // Error handling without console.error
  }

  // Last resort - check if there's a payment_link in the session and try to find any user
  // with a recent login that might match this customer

  try {
    // Get the most recent active user as a fallback
    const { data: recentUsers } = await supabase
      .from('profiles')
      .select('id')
      .order('updated_at', { ascending: false })
      .limit(10);

    if (recentUsers && recentUsers.length > 0) {
      const fallbackUserId = recentUsers[0].id;

      // Store this association for future lookups
      await supabase
        .from('customers')
        .insert({
          user_id: fallbackUserId,
          stripe_customer_id: customerId,
          created_at: new Date().toISOString()
        });

      // Update Stripe customer with this user ID - try both instances
      try {
        await stripe.customers.update(customerId, {
          metadata: {
            user_id: fallbackUserId
          }
        });
      } catch (err) {
        // If current fails, try with legacy
        const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
          apiVersion: '2023-10-16',
          httpClient: Stripe.createFetchHttpClient(),
        });
        await legacyStripe.customers.update(customerId, {
          metadata: {
            user_id: fallbackUserId
          }
        });
      }

      return fallbackUserId;
    }
  } catch (error) {
    // Error handling without console.error
  }

  return null;
};

// Function to revoke user access
const revokeUserAccess = async (userId: string) => {
  try {
    // 1. Update profile to remove subscription type
    const { error: profileError } = await supabase
      .from('profiles')
      .update({
        subscription_type: null,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      throw new Error(`Failed to update profile when revoking access: ${profileError.message}`);
    }

    // 2. Set user tokens to 0
    await updateUserTokens(userId, PLAN_TYPES.PREMIUM, false, true);

    return true;
  } catch (error) {
    throw error;
  }
};

// Function to update user's subscription
const updateSubscription = async (userId: string, subscription: any) => {
  try {
    // Get price ID and determine plan type
    const priceId = subscription.items.data[0].price.id;
    const planType = PRICE_ID_TO_PLAN[priceId];

    if (!planType) {
      throw new Error(`Invalid price ID: ${priceId}`);
    }

    // First check if subscription record exists
    const { data: existingSubscription, error: checkError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (checkError && checkError.code !== 'PGRST116') {
      throw new Error(`Failed to check existing subscription: ${checkError.message}`);
    }

    // Update or insert subscription record based on existence
    const subscriptionData = {
      user_id: userId,
      stripe_subscription_id: subscription.id,
      stripe_customer_id: subscription.customer,
      stripe_price_id: priceId,
      status: subscription.status,
      current_period_end: subscription.current_period_end,
      cancel_at_period_end: subscription.cancel_at_period_end,
    };

    // First update/insert subscription record
    const { error: subscriptionError } = await supabase
      .from('subscriptions')
      .upsert(subscriptionData, { onConflict: 'user_id' });

    if (subscriptionError) {
      throw new Error(`Failed to update subscription record: ${subscriptionError.message}`);
    }

    const validStatuses = ['active', 'incomplete', 'past_due'];

    if (validStatuses.includes(subscription.status)) {
      // Update profile with new subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          subscription_type: planType,
          has_seen_onboarding: true,
          updated_at: new Date().toISOString()
        });

      if (profileError) {
        throw new Error(`Failed to update profile: ${profileError.message}`);
      }

      // Update user tokens
      try {
        await updateUserTokens(userId, planType, false);
      } catch (tokenError) {
        throw new Error(`Failed to update user tokens: ${tokenError.message}`);
      }

      // Add delay before verification to allow for database consistency
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Verify the update
      const { data: verifyProfile, error: verifyError } = await supabase
        .from('profiles')
        .select('subscription_type, has_seen_onboarding')
        .eq('id', userId)
        .single();

      if (verifyError) {
        throw new Error(`Profile verification query failed: ${verifyError.message}`);
      }

      if (!verifyProfile) {
        throw new Error('Profile verification failed: Profile not found after update');
      }

      if (verifyProfile.subscription_type !== planType || verifyProfile.has_seen_onboarding !== true) {
        // One more attempt to update the profile
        const { error: retryError } = await supabase
          .from('profiles')
          .update({
            subscription_type: planType,
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (retryError) {
          throw new Error(`Profile verification failed after retry: ${retryError.message}`);
        }

        // Final verification
        const { data: finalVerify } = await supabase
          .from('profiles')
          .select('subscription_type, has_seen_onboarding')
          .eq('id', userId)
          .single();

        if (!finalVerify || finalVerify.subscription_type !== planType || finalVerify.has_seen_onboarding !== true) {
          throw new Error(`Profile verification failed after retry`);
        }
      }

      return planType;
    } else {
      // Invalid status, removing subscription type
      const { error: profileError } = await supabase
        .from('profiles')
        .update({ subscription_type: null })
        .eq('id', userId);

      if (profileError) {
        throw new Error(`Failed to update profile to null: ${profileError.message}`);
      }

      return null;
    }
  } catch (error) {
    throw error;
  }
};



serve(async (req) => {
  // CORS headers for cross-origin requests
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': '*',
    'Access-Control-Allow-Methods': '*'
  };

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const body = await req.text();

    let event;

    // Parse and verify the event
    if (!BYPASS_SIGNATURE_VERIFICATION) {
      const signature = req.headers.get('stripe-signature');
      if (!signature) {
        return new Response(JSON.stringify({ error: 'No signature provided' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      // Try to verify with both webhook secrets
      let verificationSucceeded = false;
      let verifiedEvent = null;

      // First try with new webhook secret
      try {
        verifiedEvent = await stripe.webhooks.constructEventAsync(
          body,
          signature,
          STRIPE_CONFIGS.current.webhookSecret,
          undefined,
          cryptoProvider
        );
        verificationSucceeded = true;
      } catch (err) {
        // If new fails, try with legacy webhook secret
        try {
          const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
            apiVersion: '2023-10-16',
            httpClient: Stripe.createFetchHttpClient(),
          });

          verifiedEvent = await legacyStripe.webhooks.constructEventAsync(
            body,
            signature,
            STRIPE_CONFIGS.legacy.webhookSecret,
            undefined,
            cryptoProvider
          );
          verificationSucceeded = true;
        } catch (legacyErr) {
          // Both failed
          verificationSucceeded = false;
        }
      }

      if (!verificationSucceeded || !verifiedEvent) {
        return new Response(JSON.stringify({ error: `Webhook signature verification failed` }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      event = verifiedEvent;
    } else {
      // BYPASS: Parse the event directly
      event = JSON.parse(body);
    }



    // Handle specific events
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object;
        const customerId = session.customer as string;

        // Check if this is a marketplace purchase
        if (session.metadata?.agent_id) {
          console.log('🛒 MARKETPLACE PURCHASE DETECTED:', {
            sessionId: session.id,
            paymentStatus: session.payment_status,
            metadata: session.metadata,
            paymentIntent: session.payment_intent,
            customer: session.customer
          });

          // Only process if payment is completed
          if (session.payment_status !== 'paid') {
            console.log('⏳ Payment not yet completed, skipping processing');
            break;
          }

          const agentId = session.metadata.agent_id;
          const buyerId = session.metadata.buyer_id;
          const sellerId = session.metadata.seller_id;
          const customName = session.metadata.custom_name;

          if (!agentId || !buyerId || !sellerId) {
            console.error('❌ Missing required metadata for marketplace purchase:', {
              agentId,
              buyerId,
              sellerId,
              allMetadata: session.metadata
            });
            break;
          }

          console.log('✅ All required metadata present, proceeding with purchase processing');

          // Check if this purchase has already been processed
          const { data: existingPurchase } = await supabase
            .from('purchased_agents')
            .select('id')
            .eq('buyer_id', buyerId)
            .eq('original_agent_id', agentId)
            .single();

          if (existingPurchase) {
            console.log('⚠️ Purchase already processed, skipping duplicate');
            break;
          }

          try {
            console.log('💳 Retrieving payment intent:', session.payment_intent);

            // Get the payment intent to get amount details
            const paymentIntent = await marketplaceStripe.paymentIntents.retrieve(session.payment_intent as string);

            console.log('💰 Payment intent details:', {
              id: paymentIntent.id,
              amount: paymentIntent.amount,
              applicationFee: paymentIntent.application_fee_amount,
              status: paymentIntent.status
            });

            console.log('🔍 Looking up original agent:', agentId);

            // Get original agent
            const { data: originalAgent, error: agentError } = await supabase
              .from('agents')
              .select('*')
              .eq('id', agentId)
              .single();

            if (agentError || !originalAgent) {
              console.error('❌ Original agent not found:', {
                agentId,
                error: agentError
              });
              break;
            }

            console.log('✅ Found original agent:', {
              id: originalAgent.id,
              name: originalAgent.name,
              sellerId: originalAgent.user_id
            });

            // Create a copy of the agent for the buyer
            const agentName = customName || `${originalAgent.name} (Purchased)`;

            console.log('📝 Creating agent copy for buyer:', {
              buyerId,
              agentName,
              originalAgentId: agentId
            });

            const { data: newAgent, error: newAgentError } = await supabase
              .from('agents')
              .insert({
                user_id: buyerId,
                name: agentName,
                description: originalAgent.description,
                configuration: originalAgent.configuration,
                is_public: false,
                is_for_sale: false,
                price: null,
                tags: originalAgent.tags || [],
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
              })
              .select()
              .single();

            if (newAgentError) {
              console.error('❌ Failed to create agent copy:', {
                error: newAgentError,
                buyerId,
                agentName,
                originalAgentId: agentId
              });
              // Don't break here, continue with transaction recording
              // The user paid, so we need to record the transaction even if agent creation fails
            }

            if (!newAgent) {
              console.error('❌ No agent returned from insert operation');
            } else {
              console.log('✅ Agent copy created successfully:', {
                newAgentId: newAgent.id,
                name: newAgent.name,
                buyerId: newAgent.user_id
              });
            }

            console.log('✅ Agent copy created successfully:', {
              newAgentId: newAgent.id,
              name: newAgent.name,
              buyerId: newAgent.user_id
            });

            // Store transaction record
            const platformFee = paymentIntent.application_fee_amount || 0;
            const totalAmount = paymentIntent.amount;
            const sellerAmount = totalAmount - platformFee;

            console.log('💰 Recording transaction:', {
              totalAmount: totalAmount / 100,
              platformFee: platformFee / 100,
              sellerAmount: sellerAmount / 100,
              paymentIntentId: paymentIntent.id,
              sessionId: session.id
            });

            const { error: transactionError } = await supabase
              .from('marketplace_transactions')
              .insert({
                buyer_id: buyerId,
                seller_id: sellerId,
                agent_id: agentId,
                stripe_payment_intent_id: paymentIntent.id,
                stripe_session_id: session.id,
                amount_total: totalAmount / 100,
                platform_fee: platformFee / 100,
                seller_amount: sellerAmount / 100,
                currency: 'usd',
                status: 'completed'
              });

            if (transactionError) {
              console.error('❌ Failed to store transaction:', transactionError);
            } else {
              console.log('✅ Transaction recorded successfully');
            }

            // Create purchased agent record (only if agent was created successfully)
            if (newAgent?.id) {
              const { error: purchaseError } = await supabase
                .from('purchased_agents')
                .insert({
                  buyer_id: buyerId,
                  seller_id: sellerId,
                  original_agent_id: agentId,
                  purchased_agent_id: newAgent.id,
                  purchase_price: totalAmount / 100,
                  custom_name: customName,
                  created_at: new Date().toISOString()
                });

              if (purchaseError) {
                console.error('❌ Failed to create purchase record:', purchaseError);
              } else {
                console.log('✅ Purchase record created successfully');
              }
            } else {
              console.log('⚠️ Skipping purchase record creation - no agent created');
            }

            // Create seller earnings record
            const { error: earningsError } = await supabase
              .from('seller_earnings')
              .insert({
                seller_id: sellerId,
                amount: sellerAmount / 100,
                currency: 'usd',
                status: 'available',
                stripe_payment_intent_id: paymentIntent.id
              });

            if (earningsError) {
              console.error('Failed to create earnings record:', earningsError);
            }

            // Update agent sales count
            const { error: salesError } = await supabase
              .from('agents')
              .update({
                sales_count: (originalAgent.sales_count || 0) + 1,
                updated_at: new Date().toISOString()
              })
              .eq('id', agentId);

            if (salesError) {
              console.error('Failed to update sales count:', salesError);
            }

            console.log('🎉 MARKETPLACE PURCHASE COMPLETED:', {
              sessionId: session.id,
              agentId,
              buyerId,
              sellerId,
              newAgentId: newAgent?.id || 'FAILED',
              agentName: newAgent?.name || 'FAILED',
              amount: totalAmount / 100,
              platformFee: platformFee / 100,
              sellerEarnings: sellerAmount / 100,
              agentCreated: !!newAgent,
              transactionRecorded: true
            });

          } catch (error) {
            console.error('Error processing marketplace purchase:', error);
          }

          break;
        }

        // Regular subscription checkout handling
        if (!customerId) {
          break;
        }

        // Try to get user ID from various sources
        let userId = session.metadata?.user_id || session.client_reference_id;

        // If we still don't have a user ID, try our customer lookup
        if (!userId) {
          userId = await getUserIdFromCustomer(customerId);

          if (!userId) {
            break;
          }
        }

        // Store customer info if not already stored
        await supabase
          .from('customers')
          .upsert({
            user_id: userId,
            stripe_customer_id: customerId,
            created_at: new Date().toISOString()
          });

        // If there's a subscription ID in the session, retrieve and store it
        if (session.subscription) {
          try {
            let subscription;

            // Try to retrieve subscription with current Stripe instance first
            try {
              subscription = await stripe.subscriptions.retrieve(session.subscription);
            } catch (err) {
              // If that fails, try with legacy Stripe instance
              const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
                apiVersion: '2023-10-16',
                httpClient: Stripe.createFetchHttpClient(),
              });
              subscription = await legacyStripe.subscriptions.retrieve(session.subscription);
            }

            await updateSubscription(userId, subscription);
          } catch (error) {
            throw error;
          }
        } else {
          // For one-time payments, still mark has_seen_onboarding as true
          try {
            const { error } = await supabase
              .from('profiles')
              .upsert({
                id: userId,
                has_seen_onboarding: true,
                updated_at: new Date().toISOString()
              });

            if (error) {
              console.error('Error updating profile on checkout completion:', error);
            }
          } catch (error) {
            console.error('Error processing checkout completion:', error);
          }
        }

        break;
      }

      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Handle subscription update
        await updateSubscription(userId, subscription);

        break;
      }

      case 'customer.subscription.deleted': {
        const subscription = event.data.object;
        const customerId = subscription.customer as string;

        if (!customerId) {
          break;
        }

        // Get user ID from customer ID
        const userId = await getUserIdFromCustomer(customerId);

        if (!userId) {
          break;
        }

        // Immediately revoke access when subscription is deleted
        await revokeUserAccess(userId);

        break;
      }

      // Marketplace payment events
      case 'payment_intent.succeeded': {
        const paymentIntent = event.data.object;

        // Check if this is a marketplace payment (has agent_id in metadata)
        if (paymentIntent.metadata?.agent_id) {
          try {
            // Update transaction status
            const { error: updateError } = await supabase
              .from('marketplace_transactions')
              .update({
                status: 'completed',
                stripe_status: paymentIntent.status,
                updated_at: new Date().toISOString()
              })
              .eq('stripe_payment_intent_id', paymentIntent.id);

            if (updateError) {
              console.error('Error updating marketplace transaction:', updateError);
            }

            // The actual agent copy creation and purchase record will be handled
            // by the confirm-payment endpoint when the frontend calls it
          } catch (error) {
            console.error('Error processing marketplace payment:', error);
          }
        }
        break;
      }

      case 'payment_intent.payment_failed': {
        const paymentIntent = event.data.object;

        // Check if this is a marketplace payment
        if (paymentIntent.metadata?.agent_id) {
          try {
            // Update transaction status
            const { error: updateError } = await supabase
              .from('marketplace_transactions')
              .update({
                status: 'failed',
                stripe_status: paymentIntent.status,
                updated_at: new Date().toISOString()
              })
              .eq('stripe_payment_intent_id', paymentIntent.id);

            if (updateError) {
              console.error('Error updating failed marketplace transaction:', updateError);
            }
          } catch (error) {
            console.error('Error processing failed marketplace payment:', error);
          }
        }
        break;
      }

      // Stripe Connect account events
      case 'account.updated': {
        const account = event.data.object;

        try {
          // Update seller account status
          const { error: updateError } = await supabase
            .from('seller_accounts')
            .update({
              account_status: account.details_submitted ? 'active' : 'pending',
              charges_enabled: account.charges_enabled,
              payouts_enabled: account.payouts_enabled,
              details_submitted: account.details_submitted,
              updated_at: new Date().toISOString()
            })
            .eq('stripe_account_id', account.id);

          if (updateError) {
            console.error('Error updating seller account:', updateError);
          }
        } catch (error) {
          console.error('Error processing account update:', error);
        }
        break;
      }
    }

    return new Response(JSON.stringify({ received: true }), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});