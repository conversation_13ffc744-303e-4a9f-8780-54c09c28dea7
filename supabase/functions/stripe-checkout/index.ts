import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "npm:stripe"

// Stripe configurations - Using only the old/legacy account for all users
const STRIPE_CONFIGS = {
  // Using STRIPE_SECRET_KEY (old account) for all users - generates #fidpam URLs
  legacy: {
    publicKey: 'pk_live_51Qq1bADebmd1GpTvtrUIe0foCjJicP6lNCnMklfLEVs85JSDQqSEVQUmgp2OpR1oYwgcvSOV5oJOx4tzhv4TZZ5W00CXhlInPx',
    secretKey: Deno.env.get('STRIPE_SECRET_KEY') || '',
  }
};

// All users use legacy account now - no need for date-based logic

// Default Stripe instance (using legacy config for all users)
const stripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
  apiVersion: '2023-10-16',
  httpClient: Stripe.createFetchHttpClient(),
});

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// REMOVED: fixUrlHashForNewAccount function was causing URL corruption
// We now return the original session.url directly from Stripe without modification

// Constants for plans and limits - Premium only
const PLAN_TYPES = {
  PREMIUM: 'premium'
};

// Price ID to plan mapping - Using only legacy account price IDs for all users
const PRICE_ID_TO_PLAN = {
  // Legacy account price IDs - These generate URLs with #fidpam
  'price_1ROYLKDebmd1GpTvct491Kw6': PLAN_TYPES.PREMIUM, // Weekly Premium
  'price_1RVyr7Debmd1GpTvgWmmS7R1': PLAN_TYPES.PREMIUM, // Annual Premium
};



serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    })
  }

  try {
    // Parse the request body
    let body;
    try {
      body = await req.json()
    } catch (e) {
      return new Response(JSON.stringify({ error: 'Invalid request body' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    const { action, priceId, returnUrl, couponId } = body
    const authHeader = req.headers.get('Authorization')

    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'No authorization header' }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Get the authenticated user's ID
    const { data: { user }, error: userError } = await supabase.auth.getUser(authHeader.replace('Bearer ', ''))

    if (userError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: userError?.message }), {
        status: 401,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

    // Handle different actions
    switch (action) {
      case 'create-checkout-session': {
        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        // Validate that the price ID exists in the target Stripe account
        try {
          const price = await userStripe.prices.retrieve(priceId);
          console.log('✅ Price ID validation successful:', {
            priceId,
            active: price.active,
            currency: price.currency,
            type: price.type,
            recurring: price.recurring
          });

          if (!price.active) {
            throw new Error(`Price ID ${priceId} is inactive in the target Stripe account`);
          }
        } catch (priceError) {
          console.error('❌ Price ID validation failed:', {
            priceId,
            stripeConfig: 'legacy',
            error: priceError.message
          });

          return new Response(JSON.stringify({
            error: `Price ID ${priceId} not found in the legacy Stripe account.`
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        // Get or create customer
        const { data: customer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        let customerId = customer?.stripe_customer_id

        if (!customerId) {
          // No customer record exists, create a new one
          const newCustomer = await userStripe.customers.create({
            email: user.email,
            metadata: {
              user_id: user.id,
            },
          })
          customerId = newCustomer.id

          await supabase.from('customers').insert({
            user_id: user.id,
            stripe_customer_id: customerId,
          })
        } else {
          // Customer record exists, but verify it exists in the correct Stripe account
          try {
            await userStripe.customers.retrieve(customerId);
          } catch (error) {
            // Customer doesn't exist in this Stripe account, create a new one
            console.log(`Customer ${customerId} not found in legacy Stripe account, creating new customer`);

            const newCustomer = await userStripe.customers.create({
              email: user.email,
              metadata: {
                user_id: user.id,
                migrated_from: customerId, // Track the old customer ID
              },
            })

            // Update the database with the new customer ID
            await supabase
              .from('customers')
              .update({ stripe_customer_id: newCustomer.id })
              .eq('user_id', user.id)

            customerId = newCustomer.id;
          }
        }

        // Create checkout session URLs with proper parameter handling
        const baseUrl = returnUrl || `${req.headers.get('origin') || 'https://app.Osis.co'}`;
        const hasExistingParams = baseUrl.includes('?');
        const paramSeparator = hasExistingParams ? '&' : '?';

        const successUrl = `${baseUrl}${paramSeparator}session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(customerId)}`;
        const cancelUrl = `${baseUrl}${paramSeparator}canceled=true`;

        console.log('🔗 Checkout URLs:', {
          baseUrl,
          hasExistingParams,
          successUrl,
          cancelUrl
        });

        // Check if request is from mobile device
        const userAgent = req.headers.get('user-agent') || '';
        const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

        // Get country from headers if available
        const acceptLanguage = req.headers.get('accept-language') || '';
        const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';

        const sessionConfig: Stripe.Checkout.SessionCreateParams = {
          customer: customerId,
          payment_method_types: ['card'],
          line_items: [
            {
              price: priceId,
              quantity: 1,
            },
          ],
          mode: 'subscription',
          success_url: successUrl,
          cancel_url: cancelUrl,
          allow_promotion_codes: true,
          metadata: {
            user_id: user.id,
            created_at: new Date().toISOString(),
            is_mobile: isMobile ? 'true' : 'false',
            country_code: countryCode || 'unknown',
            user_created_at: userCreatedAt,
            stripe_config: 'legacy',
          },
          client_reference_id: user.id,
          // Add support for international users
          billing_address_collection: 'auto', // Always use 'auto' for better international support
          locale: 'auto', // Automatically detect user's locale
          // Simplify the checkout for mobile and international users
          phone_number_collection: {
            enabled: false,
          },
        };

        // No trial period - users pay immediately

        // Add coupon if provided
        if (couponId) {
          sessionConfig.discounts = [
            {
              coupon: couponId,
            },
          ];
        }

        // Log the session configuration before creating
        console.log('🔧 Creating Stripe checkout session with config:', {
          customer: customerId,
          priceId,
          userCreatedAt,
          stripeConfig: 'legacy',
          publicKey: STRIPE_CONFIGS.legacy.publicKey.substring(0, 20) + '...',
          sessionConfigSummary: {
            customer: sessionConfig.customer,
            mode: sessionConfig.mode,
            lineItems: sessionConfig.line_items,
            successUrl: sessionConfig.success_url,
            cancelUrl: sessionConfig.cancel_url
          }
        });

        // Validate that we have the correct Stripe instance and keys
        if (!userStripe || !STRIPE_CONFIGS.legacy.secretKey) {
          throw new Error('Stripe configuration is invalid or missing');
        }

        const session = await userStripe.checkout.sessions.create(sessionConfig);

        // Validate the session was created successfully
        if (!session || !session.id || !session.url) {
          throw new Error('Failed to create valid Stripe checkout session');
        }

        // Validate the URL before returning it
        const isValidUrl = session.url && session.url.includes('checkout.stripe.com') && session.url.includes('/c/pay/');

        console.log('✅ Stripe checkout session created successfully:', {
          sessionId: session.id,
          url: session.url,
          urlValid: isValidUrl,
          urlLength: session.url?.length || 0,
          status: session.status,
          customer: session.customer,
          mode: session.mode,
          stripeAccount: 'legacy'
        });

        // Log URL details for debugging
        if (session.url) {
          try {
            const urlObj = new URL(session.url);
            console.log('🔍 URL Analysis:', {
              protocol: urlObj.protocol,
              hostname: urlObj.hostname,
              pathname: urlObj.pathname,
              hash: urlObj.hash ? `${urlObj.hash.substring(0, 50)}...` : 'none',
              hashLength: urlObj.hash?.length || 0,
              fullUrlLength: session.url.length
            });
          } catch (urlError) {
            console.error('❌ URL parsing error:', urlError);
          }
        }

        // REMOVED: sessionMetadata - not needed for bare bones

        // BARE BONES: Direct passthrough
        return new Response(JSON.stringify({
          sessionId: session.id,
          url: session.url
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'validate-session': {
        const { sessionId } = body;

        if (!sessionId) {
          return new Response(JSON.stringify({ error: 'Session ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        try {
          // Try to retrieve the session with both Stripe configurations
          let session: any = null;
          let stripeConfig = 'current';

          try {
            // Use legacy config for all users now
            const legacyStripe = new Stripe(STRIPE_CONFIGS.legacy.secretKey, {
              apiVersion: '2023-10-16',
              httpClient: Stripe.createFetchHttpClient(),
            });
            session = await legacyStripe.checkout.sessions.retrieve(sessionId);
            stripeConfig = 'legacy';
          } catch (legacyError) {
            throw new Error(`Session not found in legacy Stripe account: ${legacyError.message}`);
          }

          if (!session) {
            throw new Error('Session is null after retrieval');
          }

          return new Response(JSON.stringify({
            valid: true,
            session: {
              id: session.id,
              status: session.status,
              payment_status: session.payment_status,
              url: session.url
            },
            stripeConfig
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (error) {
          console.error('❌ Session validation failed:', error);
          return new Response(JSON.stringify({
            valid: false,
            error: error.message
          }), {
            status: 404,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      case 'create-payment-link': {
        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Create a payment link
        const paymentLink = await stripe.paymentLinks.create({
          line_items: [
            {
              price: priceId,
              quantity: 1,
            },
          ],
          allow_promotion_codes: true,
          after_completion: {
            type: 'redirect',
            redirect: {
              url: returnUrl || `${req.headers.get('origin') || 'https://app.Osis.co'}?payment_success=true`,
            },
          },
        });

        return new Response(JSON.stringify({ url: paymentLink.url }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        });
      }

      case 'create-customer': {
        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        // Check if customer already exists
        const { data: existingCustomer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        if (existingCustomer?.stripe_customer_id) {
          // Verify the customer exists in the correct Stripe account
          try {
            await userStripe.customers.retrieve(existingCustomer.stripe_customer_id);
            return new Response(JSON.stringify({ customerId: existingCustomer.stripe_customer_id }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            })
          } catch (error) {
            // Customer doesn't exist in this Stripe account, create a new one
            console.log(`Customer ${existingCustomer.stripe_customer_id} not found in legacy Stripe account, creating new customer`);

            const newCustomer = await userStripe.customers.create({
              email: user.email,
              metadata: {
                user_id: user.id,
                migrated_from: existingCustomer.stripe_customer_id,
              },
            })

            // Update the database with the new customer ID
            await supabase
              .from('customers')
              .update({ stripe_customer_id: newCustomer.id })
              .eq('user_id', user.id)

            return new Response(JSON.stringify({ customerId: newCustomer.id }), {
              headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            })
          }
        }

        // Create new customer
        const customer = await userStripe.customers.create({
          email: user.email,
          metadata: {
            user_id: user.id,
          },
        })

        // Store customer ID in database
        await supabase.from('customers').insert({
          user_id: user.id,
          stripe_customer_id: customer.id,
        })

        return new Response(JSON.stringify({ customerId: customer.id }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'create-subscription': {
        const { priceId } = body

        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        // Get or create customer
        const { data: customer } = await supabase
          .from('customers')
          .select('stripe_customer_id')
          .eq('user_id', user.id)
          .single()

        let customerId = customer?.stripe_customer_id

        if (!customerId) {
          // No customer record exists, create a new one
          const newCustomer = await userStripe.customers.create({
            email: user.email,
            metadata: {
              user_id: user.id,
            },
          })
          customerId = newCustomer.id

          await supabase.from('customers').insert({
            user_id: user.id,
            stripe_customer_id: customerId,
          })
        } else {
          // Customer record exists, but verify it exists in the correct Stripe account
          try {
            await userStripe.customers.retrieve(customerId);
          } catch (error) {
            // Customer doesn't exist in this Stripe account, create a new one
            console.log(`Customer ${customerId} not found in legacy Stripe account, creating new customer`);

            const newCustomer = await userStripe.customers.create({
              email: user.email,
              metadata: {
                user_id: user.id,
                migrated_from: customerId, // Track the old customer ID
              },
            })

            // Update the database with the new customer ID
            await supabase
              .from('customers')
              .update({ stripe_customer_id: newCustomer.id })
              .eq('user_id', user.id)

            customerId = newCustomer.id;
          }
        }

        // Create checkout session for subscription
        try {
          // Update redirect URLs to the specified domain
          const baseRedirectUrl = "https://app.Osis.co";
          const successUrl = `${baseRedirectUrl}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(customerId)}`;
          const cancelUrl = `${baseRedirectUrl}?canceled=true`;

          // Check if request is from mobile device
          const userAgent = req.headers.get('user-agent') || '';
          const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

          // Get country from headers if available
          const acceptLanguage = req.headers.get('accept-language') || '';
          const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';

          const session = await userStripe.checkout.sessions.create({
            customer: customerId,
            payment_method_types: ['card'],
            line_items: [
              {
                price: priceId,
                quantity: 1,
              },
            ],
            mode: 'subscription',
            success_url: successUrl,
            cancel_url: cancelUrl,
            allow_promotion_codes: true,
            metadata: {
              user_id: user.id,
              created_at: new Date().toISOString(),
              is_mobile: isMobile ? 'true' : 'false',
              country_code: countryCode || 'unknown',
              user_created_at: userCreatedAt,
              stripe_config: 'legacy',
            },
            client_reference_id: user.id,
            // Add support for international users
            billing_address_collection: 'auto', // Always use 'auto' for better international support
            locale: 'auto', // Automatically detect user's locale
            // Simplify the checkout for mobile and international users
            phone_number_collection: {
              enabled: false,
            },
          });

          // BARE BONES: Direct passthrough
          return new Response(JSON.stringify({
            sessionId: session.id,
            url: session.url
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      case 'get-subscription': {
        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ subscription: null }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get the latest subscription data from Stripe
        let stripeSubscription;
        try {
          stripeSubscription = await userStripe.subscriptions.retrieve(
            subscription.stripe_subscription_id
          );
        } catch (error) {
          // If we can't get the subscription from Stripe, use the data we have
          stripeSubscription = null;
        }

        // Determine if we need to update our subscription data based on Stripe's data
        if (stripeSubscription) {
          const updates: Record<string, any> = {};
          let needsUpdate = false;

          // Check if the status changed
          if (stripeSubscription.status !== subscription.status) {
            updates.status = stripeSubscription.status;
            updates.current_period_end = stripeSubscription.current_period_end;
            updates.cancel_at_period_end = stripeSubscription.cancel_at_period_end;
            needsUpdate = true;
          }

          // Check if the price changed
          if (stripeSubscription.items?.data?.length > 0) {
            const stripePriceId = stripeSubscription.items.data[0].price.id;
            if (stripePriceId !== subscription.stripe_price_id) {
              updates.stripe_price_id = stripePriceId;
              needsUpdate = true;
            }
          }

          // Update the database if needed
          if (needsUpdate) {
            await supabase.from('subscriptions').update(updates)
              .eq('stripe_subscription_id', subscription.stripe_subscription_id);

            // Update our local copy
            Object.assign(subscription, updates);
          }
        }

        // Determine the subscription type based on the price ID - REGARDLESS of status
        // This ensures consistent plan type throughout the system
        let subscription_type = PLAN_TYPES.PREMIUM;
        if (subscription.stripe_price_id) {
          // IMPORTANT: Consider any subscription with a valid price ID as active,
          // even if its status is 'incomplete' or 'past_due'
          const validStatuses = ['active', 'incomplete', 'past_due'];

          // Only use the subscription price ID if the status is valid
          // or if it's a new incomplete subscription
          if (validStatuses.includes(subscription.status)) {
            subscription_type = PRICE_ID_TO_PLAN[subscription.stripe_price_id] || PLAN_TYPES.PREMIUM;
          }
        }

        // Make sure the user's profile has the correct subscription type - ALWAYS update
        const { data: profile } = await supabase
          .from('profiles')
          .select('subscription_type')
          .eq('id', user.id)
          .single();

        // If the profile's subscription_type doesn't match, update it
        if (profile && profile.subscription_type !== subscription_type) {
          await supabase
            .from('profiles')
            .update({ subscription_type })
            .eq('id', user.id);
        }

        return new Response(JSON.stringify({
          subscription: {
            ...subscription,
            stripe_subscription: stripeSubscription,
            subscription_type
          },
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'cancel-subscription': {
        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        // Get the user's current subscription
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ error: 'No active subscription found' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Cancel the subscription at period end
        const canceledSubscription = await userStripe.subscriptions.update(
          subscription.stripe_subscription_id,
          { cancel_at_period_end: true }
        )

        // Update the subscription in the database
        await supabase.from('subscriptions').update({
          cancel_at_period_end: true
        }).eq('stripe_subscription_id', subscription.stripe_subscription_id)

        return new Response(JSON.stringify({
          subscription: canceledSubscription,
          accessRevoked: false
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }

      case 'update-subscription': {
        const { priceId } = body

        if (!priceId) {
          return new Response(JSON.stringify({ error: 'Price ID is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        // Get user's creation date to determine which Stripe config to use
        const { data: userProfile } = await supabase
          .from('profiles')
          .select('created_at')
          .eq('id', user.id)
          .single()

        // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
        let userCreatedAt = userProfile?.created_at;
        if (!userCreatedAt) {
          // Query auth.users table for creation date
          const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
          userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
        }

        // Use legacy Stripe instance for all users
        const userStripe = stripe;

        // Get the user's current subscription
        const { data: subscription } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .single()

        if (!subscription) {
          return new Response(JSON.stringify({ error: 'No active subscription found' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }

        try {
          // Create a checkout session for the subscription update
          const baseRedirectUrl = "https://app.Osis.co";
          const successUrl = `${baseRedirectUrl}?session_id={CHECKOUT_SESSION_ID}&success=true&customer_id=${encodeURIComponent(subscription.stripe_customer_id)}`;
          const cancelUrl = `${baseRedirectUrl}?canceled=true`;

          // Check if request is from mobile device
          const userAgent = req.headers.get('user-agent') || '';
          const isMobile = /iPhone|iPad|iPod|Android/i.test(userAgent);

          // Get country from headers if available
          const acceptLanguage = req.headers.get('accept-language') || '';
          const countryCode = acceptLanguage.split(',')[0].split('-')[1]?.toUpperCase() || '';

          // Create a checkout session for the subscription update
          const session = await userStripe.checkout.sessions.create({
            customer: subscription.stripe_customer_id,
            payment_method_types: ['card'],
            line_items: [
              {
                price: priceId,
                quantity: 1,
              },
            ],
            mode: 'subscription',
            success_url: successUrl,
            cancel_url: cancelUrl,
            allow_promotion_codes: true,
            metadata: {
              user_id: user.id,
              created_at: new Date().toISOString(),
              update_type: 'plan_change',
              previous_subscription_id: subscription.stripe_subscription_id,
              previous_price_id: subscription.stripe_price_id,
              is_mobile: isMobile ? 'true' : 'false',
              country_code: countryCode || 'unknown',
              user_created_at: userCreatedAt,
              stripe_config: 'legacy',
            },
            client_reference_id: user.id,
            // Add support for international users
            billing_address_collection: 'auto',
            locale: 'auto',
            phone_number_collection: {
              enabled: false,
            },
            // This will cancel the old subscription when the new one is created
            subscription_data: {
              // Transfer metadata from old subscription
              metadata: {
                user_id: user.id,
                supabase_user_id: user.id
              }
            }
          });

          // CRITICAL FIX: Return the original session.url from Stripe without any modification
          console.log('🔧 DEBUGGING - Original Stripe URL (update-subscription):', {
            sessionId: session.id,
            originalUrl: session.url,
            urlLength: session.url?.length || 0,
            timestamp: new Date().toISOString()
          });

          return new Response(JSON.stringify({
            sessionId: session.id,
            url: session.url // Use original URL directly from Stripe
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      case 'handle-checkout-redirect': {
        const { sessionId, customerId } = body;

        if (!sessionId) {
          return new Response(JSON.stringify({ error: 'No session ID provided' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }

        try {
          // Get user's creation date to determine which Stripe config to use
          const { data: userProfile } = await supabase
            .from('profiles')
            .select('created_at')
            .eq('id', user.id)
            .single()

          // Use user's creation date, fallback to auth.users created_at if profile doesn't exist
          let userCreatedAt = userProfile?.created_at;
          if (!userCreatedAt) {
            // Query auth.users table for creation date
            const { data: authUser } = await supabase.auth.admin.getUserById(user.id);
            userCreatedAt = authUser?.user?.created_at || new Date().toISOString();
          }

          // Use legacy Stripe instance for all users
          const userStripe = stripe;

          // Retrieve checkout session
          const session = await userStripe.checkout.sessions.retrieve(sessionId, {
            expand: ['customer', 'subscription']
          });

          // Make sure we have a user_id associated with this customer
          let userId = user.id; // Default to the authenticated user

          // Use the provided customerId if available, otherwise use the one from the session
          const customerIdToUse = customerId || (typeof session.customer === 'string' ? session.customer : session.customer?.id);

          if (customerIdToUse) {
            // Check if customer metadata has user_id
            const customer = await userStripe.customers.retrieve(customerIdToUse);

            // If customer doesn't have metadata.user_id, update it
            if (!customer.metadata?.user_id) {
              await userStripe.customers.update(customer.id, {
                metadata: {
                  ...customer.metadata,
                  user_id: userId
                }
              });
            }

            // Make sure we have a customer record in database
            const { data: existingCustomer } = await supabase
              .from('customers')
              .select('*')
              .eq('stripe_customer_id', customer.id)
              .single();

            if (!existingCustomer) {
              await supabase
                .from('customers')
                .insert({
                  user_id: userId,
                  stripe_customer_id: customer.id,
                  created_at: new Date().toISOString()
                });
            }

            // If there's a subscription in the session, store it
            if (session.subscription) {
              const subscription = typeof session.subscription === 'string'
                ? await userStripe.subscriptions.retrieve(session.subscription)
                : session.subscription;

              // Determine the plan type from the price ID
              const items = subscription.items.data;
              if (items && items.length > 0) {
                const priceId = items[0].price.id;
                const planType = PRICE_ID_TO_PLAN[priceId] || PLAN_TYPES.PREMIUM;

                // Store subscription in database
                await supabase.from('subscriptions').upsert({
                  user_id: userId,
                  stripe_subscription_id: subscription.id,
                  stripe_customer_id: customer.id,
                  stripe_price_id: priceId,
                  status: subscription.status,
                  current_period_end: subscription.current_period_end,
                  cancel_at_period_end: subscription.cancel_at_period_end,
                });

                // Update profile with new subscription type REGARDLESS of status
                await supabase
                  .from('profiles')
                  .update({ subscription_type: planType })
                  .eq('id', userId);
              }
            }
          }

          return new Response(JSON.stringify({
            session: {
              id: session.id,
              customer: typeof session.customer === 'string' ? session.customer : session.customer?.id,
              subscription: typeof session.subscription === 'string' ? session.subscription : session.subscription?.id,
              status: session.status
            }
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        } catch (err) {
          return new Response(JSON.stringify({ error: err.message }), {
            status: 500,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          });
        }
      }

      default:
        return new Response(JSON.stringify({ error: 'Invalid action' }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
    }
  } catch (error) {
    console.error('Error:', error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
