-- SECURITY: Prevent users from editing, deleting, or setting prices on licensed agents
-- This migration adds additional security measures to ensure licensed agents cannot be modified

-- <PERSON><PERSON> function to check if user is trying to modify a licensed agent
CREATE OR REPLACE FUNCTION prevent_licensed_agent_modification()
RETURNS TRIGGER AS $$
DECLARE
  v_user_id UUID;
  v_license_exists BOOLEAN;
BEGIN
  -- Get the current user ID
  v_user_id := auth.uid();
  
  -- If no user, deny the operation
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'Authentication required';
  END IF;
  
  -- For UPDATE and DELETE operations, check if this is a licensed agent
  IF TG_OP IN ('UPDATE', 'DELETE') THEN
    -- Check if the user has a license for this agent (meaning they don't own it)
    SELECT EXISTS(
      SELECT 1 FROM public.agent_licenses 
      WHERE buyer_id = v_user_id 
        AND agent_id = OLD.id 
        AND is_active = TRUE
    ) INTO v_license_exists;
    
    -- If user has a license for this agent, they cannot modify it
    IF v_license_exists THEN
      RAISE EXCEPTION 'You cannot modify agents you have licensed. You can only modify agents you created.';
    END IF;
    
    -- Also ensure the user owns the agent
    IF OLD.user_id != v_user_id THEN
      RAISE EXCEPTION 'You can only modify agents you own';
    END IF;
  END IF;
  
  -- For INSERT operations, ensure user_id is set correctly
  IF TG_OP = 'INSERT' THEN
    NEW.user_id := v_user_id;
  END IF;
  
  -- For UPDATE operations, prevent changing ownership
  IF TG_OP = 'UPDATE' THEN
    -- Prevent changing user_id
    IF OLD.user_id != NEW.user_id THEN
      RAISE EXCEPTION 'Cannot change agent ownership';
    END IF;
    
    -- Ensure user owns the agent
    IF OLD.user_id != v_user_id THEN
      RAISE EXCEPTION 'You can only modify agents you own';
    END IF;
  END IF;
  
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to prevent licensed agent modification
DROP TRIGGER IF EXISTS trigger_prevent_licensed_agent_modification ON public.agents;
CREATE TRIGGER trigger_prevent_licensed_agent_modification
  BEFORE INSERT OR UPDATE OR DELETE ON public.agents
  FOR EACH ROW
  EXECUTE FUNCTION prevent_licensed_agent_modification();

-- Create function to get user's owned agents (excluding licensed ones)
CREATE OR REPLACE FUNCTION get_user_owned_agents(p_user_id UUID)
RETURNS TABLE (
  id UUID,
  name TEXT,
  description TEXT,
  configuration JSONB,
  is_public BOOLEAN,
  public_description TEXT,
  tags TEXT[],
  price DECIMAL(10,2),
  is_for_sale BOOLEAN,
  is_encrypted BOOLEAN,
  security_level INTEGER,
  sales_count INTEGER,
  likes_count INTEGER,
  created_at TIMESTAMP WITH TIME ZONE,
  updated_at TIMESTAMP WITH TIME ZONE,
  user_id UUID
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    a.id,
    a.name,
    a.description,
    a.configuration,
    a.is_public,
    a.public_description,
    a.tags,
    a.price,
    a.is_for_sale,
    a.is_encrypted,
    a.security_level,
    a.sales_count,
    a.likes_count,
    a.created_at,
    a.updated_at,
    a.user_id
  FROM public.agents a
  WHERE a.user_id = p_user_id
    -- SECURITY: Exclude any agents that the user has licenses for (shouldn't happen, but extra safety)
    AND NOT EXISTS (
      SELECT 1 FROM public.agent_licenses al 
      WHERE al.buyer_id = p_user_id 
        AND al.agent_id = a.id 
        AND al.is_active = TRUE
    )
  ORDER BY a.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user's licensed agents (separate from owned agents)
CREATE OR REPLACE FUNCTION get_user_licensed_agents(p_user_id UUID)
RETURNS TABLE (
  license_id UUID,
  license_key TEXT,
  custom_name TEXT,
  purchase_price DECIMAL(10,2),
  usage_count INTEGER,
  max_usage_limit INTEGER,
  license_expires_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE,
  agent_id UUID,
  agent_name TEXT,
  agent_description TEXT,
  agent_tags TEXT[],
  is_encrypted BOOLEAN,
  security_level INTEGER
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    al.id as license_id,
    al.license_key,
    al.custom_name,
    al.purchase_price,
    al.usage_count,
    al.max_usage_limit,
    al.license_expires_at,
    al.created_at,
    a.id as agent_id,
    a.name as agent_name,
    a.description as agent_description,
    a.tags as agent_tags,
    a.is_encrypted,
    a.security_level
  FROM public.agent_licenses al
  JOIN public.agents a ON al.agent_id = a.id
  WHERE al.buyer_id = p_user_id
    AND al.is_active = TRUE
    AND al.is_suspended = FALSE
  ORDER BY al.created_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update RLS policies to be more restrictive
DROP POLICY IF EXISTS "Users can update their own agents" ON public.agents;
CREATE POLICY "Users can update their own agents" ON public.agents
  FOR UPDATE USING (
    auth.uid() = user_id 
    AND NOT EXISTS (
      SELECT 1 FROM public.agent_licenses 
      WHERE buyer_id = auth.uid() 
        AND agent_id = agents.id 
        AND is_active = TRUE
    )
  );

DROP POLICY IF EXISTS "Users can delete their own agents" ON public.agents;
CREATE POLICY "Users can delete their own agents" ON public.agents
  FOR DELETE USING (
    auth.uid() = user_id 
    AND NOT EXISTS (
      SELECT 1 FROM public.agent_licenses 
      WHERE buyer_id = auth.uid() 
        AND agent_id = agents.id 
        AND is_active = TRUE
    )
  );

-- Grant permissions for new functions
GRANT EXECUTE ON FUNCTION get_user_owned_agents TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_licensed_agents TO authenticated;

-- Create view for user's agent library (owned agents only)
CREATE OR REPLACE VIEW user_agent_library AS
SELECT 
  a.*,
  'owned' as access_type
FROM public.agents a
WHERE a.user_id = auth.uid()
  AND NOT EXISTS (
    SELECT 1 FROM public.agent_licenses al 
    WHERE al.buyer_id = auth.uid() 
      AND al.agent_id = a.id 
      AND al.is_active = TRUE
  );

-- Create view for user's licensed agents
CREATE OR REPLACE VIEW user_licensed_agents AS
SELECT 
  al.id as license_id,
  al.license_key,
  al.custom_name,
  al.purchase_price,
  al.usage_count,
  al.max_usage_limit,
  al.license_expires_at,
  al.created_at as license_created_at,
  a.*,
  'licensed' as access_type
FROM public.agent_licenses al
JOIN public.agents a ON al.agent_id = a.id
WHERE al.buyer_id = auth.uid()
  AND al.is_active = TRUE
  AND al.is_suspended = FALSE;

-- Grant access to views
GRANT SELECT ON user_agent_library TO authenticated;
GRANT SELECT ON user_licensed_agents TO authenticated;

-- Add comment explaining the security model
COMMENT ON FUNCTION prevent_licensed_agent_modification() IS 
'SECURITY: Prevents users from modifying agents they have licensed but do not own. Users can only modify agents they created.';

COMMENT ON FUNCTION get_user_owned_agents(UUID) IS 
'SECURITY: Returns only agents that the user actually owns (created), excluding any licensed agents.';

COMMENT ON FUNCTION get_user_licensed_agents(UUID) IS 
'SECURITY: Returns only agents that the user has valid licenses for, with license metadata.';

COMMENT ON VIEW user_agent_library IS 
'SECURITY: Shows only agents the user owns (created), not licensed agents. These can be edited, deleted, and priced.';

COMMENT ON VIEW user_licensed_agents IS 
'SECURITY: Shows only agents the user has licenses for. These can only be executed securely, not modified.';
