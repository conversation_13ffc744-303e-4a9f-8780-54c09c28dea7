-- Enable UUID extension if not already enabled
create extension if not exists "uuid-ossp";

-- Drop existing triggers and functions to ensure clean slate
drop trigger if exists on_auth_user_created on auth.users;
drop trigger if exists after_user_insert on auth.users;
drop trigger if exists new_user_trigger on auth.users;
drop function if exists public.handle_new_user() cascade;
drop function if exists public.reset_daily_messages() cascade;

-- Create profiles table
create table if not exists public.profiles (
    id uuid primary key references auth.users on delete cascade,
    full_name text,
    avatar_url text,
    email text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create user_limits table with daily reset functionality
create table if not exists public.user_limits (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users on delete cascade unique not null,
    messages_used integer default 0,
    last_reset timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create customers table
create table if not exists public.customers (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users on delete cascade unique not null,
    stripe_customer_id text,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create subscriptions table
create table if not exists public.subscriptions (
    id uuid default gen_random_uuid() primary key,
    user_id uuid references auth.users on delete cascade not null,
    status text not null,
    stripe_subscription_id text,
    stripe_price_id text,
    cancel_at timestamp with time zone,
    cancel_at_period_end boolean default false,
    created_at timestamp with time zone default timezone('utc'::text, now()) not null,
    updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Function to reset daily message count
create or replace function reset_daily_messages()
returns trigger
language plpgsql
security definer
set search_path = public
as $$
declare
    user_plan text;
    plan_limit integer;
begin
    -- Get the user's subscription type from profiles
    select subscription_type into user_plan
    from profiles
    where id = new.user_id;

    -- Set plan limit based on subscription type
    case user_plan
        when 'basic' then plan_limit := 100;
        when 'pro' then plan_limit := 200;
        when 'premium' then plan_limit := 300;
        else plan_limit := 0; -- Default for no subscription
    end case;

    -- No trial functionality - users get full plan limits immediately

    -- For paid users, reset monthly
    if new.last_reset < current_timestamp - interval '1 month' then
        new.messages_used := 0;
        new.tokens_remaining := plan_limit;
        new.last_reset := current_timestamp;
    end if;
    return new;
end;
$$;

-- Create trigger to automatically reset monthly message count
create trigger trigger_reset_monthly_messages
    before update on public.user_limits
    for each row
    execute function reset_daily_messages();

-- Set up Row Level Security (RLS)
alter table public.profiles enable row level security;
alter table public.user_limits enable row level security;
alter table public.customers enable row level security;
alter table public.subscriptions enable row level security;

-- Drop existing policies to ensure clean slate
drop policy if exists "Public profiles are viewable by everyone" on public.profiles;
drop policy if exists "Users can update their own profile" on public.profiles;
drop policy if exists "Users can insert their own profile" on public.profiles;
drop policy if exists "Service role can insert profiles" on public.profiles;
drop policy if exists "Service role can update profiles" on public.profiles;
drop policy if exists "Users can view their own limits" on public.user_limits;
drop policy if exists "Users can update their own limits" on public.user_limits;
drop policy if exists "Service role can insert user limits" on public.user_limits;
drop policy if exists "Service role can update user limits" on public.user_limits;
drop policy if exists "Users can view their own customer data" on public.customers;
drop policy if exists "Service role can manage customers" on public.customers;
drop policy if exists "Users can view their own subscriptions" on public.subscriptions;
drop policy if exists "Service role can manage subscriptions" on public.subscriptions;

-- Create policies
create policy "Public profiles are viewable by everyone"
    on public.profiles for select
    using (true);

create policy "Users can update their own profile"
    on public.profiles for update
    using (auth.uid() = id);

create policy "Users can insert their own profile"
    on public.profiles for insert
    with check (auth.uid() = id);

create policy "Service role can manage profiles"
    on public.profiles for all
    to service_role
    using (true)
    with check (true);

create policy "Users can view their own limits"
    on public.user_limits for select
    using (auth.uid() = user_id);

create policy "Users can update their own limits"
    on public.user_limits for update
    using (auth.uid() = user_id);

create policy "Service role can manage user limits"
    on public.user_limits for all
    to service_role
    using (true)
    with check (true);

create policy "Users can view their own customer data"
    on public.customers for select
    using (auth.uid() = user_id);

create policy "Service role can manage customers"
    on public.customers for all
    to service_role
    using (true)
    with check (true);

create policy "Users can view their own subscriptions"
    on public.subscriptions for select
    using (auth.uid() = user_id);

create policy "Service role can manage subscriptions"
    on public.subscriptions for all
    to service_role
    using (true)
    with check (true);

-- Drop and recreate the handle_new_user function with better error handling
drop function if exists public.handle_new_user();

create function public.handle_new_user()
returns trigger
language plpgsql
security definer
set search_path = public
as $$
declare
    v_email text;
begin
    -- Get email from auth.users
    select email into v_email from auth.users where id = new.id;

    -- Create profile first (this is essential for auth)
    begin
        insert into public.profiles (id, full_name, email)
        values (
            new.id,
            coalesce(new.raw_user_meta_data->>'full_name', split_part(v_email, '@', 1)),
            v_email
        );
    exception when others then
        -- Log error but don't stop the process
        raise warning 'Error creating profile for user ID %, Error: %', new.id, SQLERRM;
    end;

    -- Try to create user limits, but don't let it block signup if it fails
    begin
        insert into public.user_limits (user_id, messages_used, last_reset)
        values (new.id, 0, now())
        on conflict (user_id) do update
        set messages_used = 0,
            last_reset = now();
    exception when others then
        -- Just log the error and continue
        raise warning 'Error creating user_limits for user ID %, Error: %', new.id, SQLERRM;
    end;

    -- Always return new to ensure the auth signup succeeds
    return new;
end;
$$;

-- Recreate the trigger
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
    after insert on auth.users
    for each row
    execute function public.handle_new_user();

-- Ensure proper permissions
grant execute on function public.handle_new_user() to service_role;